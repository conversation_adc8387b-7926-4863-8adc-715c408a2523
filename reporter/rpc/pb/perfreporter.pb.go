// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: reporter/perfreporter.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"

	pb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PerfPlanRecord struct {
	state          protoimpl.MessageState    `protogen:"open.v1"`
	TaskId         string                    `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                                          // 任务ID
	ExecuteId      string                    `protobuf:"bytes,2,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                                                 // 压测计划执行ID
	ProjectId      string                    `protobuf:"bytes,11,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                                // 项目ID
	PlanId         string                    `protobuf:"bytes,12,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                         // 计划ID
	PlanName       string                    `protobuf:"bytes,13,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                                                   // 计划名称
	TriggerMode    pb.TriggerMode            `protobuf:"varint,14,opt,name=trigger_mode,json=triggerMode,proto3,enum=common.TriggerMode" json:"trigger_mode,omitempty"`                 // 触发方式（手动、定时、接口）
	TargetMaxRps   int64                     `protobuf:"varint,15,opt,name=target_max_rps,json=targetMaxRps,proto3" json:"target_max_rps,omitempty"`                                    // 目标最大的RPS
	TargetDuration uint32                    `protobuf:"varint,16,opt,name=target_duration,json=targetDuration,proto3" json:"target_duration,omitempty"`                                // 目标压测持续时长（单位为秒）
	Protocol       pb.Protocol               `protobuf:"varint,17,opt,name=protocol,proto3,enum=common.Protocol" json:"protocol,omitempty"`                                             // 协议
	TargetEnv      pb.TargetEnvironment      `protobuf:"varint,18,opt,name=target_env,json=targetEnv,proto3,enum=common.TargetEnvironment" json:"target_env,omitempty"`                 // 目标环境
	Status         string                    `protobuf:"bytes,31,opt,name=status,proto3" json:"status,omitempty"`                                                                       // 执行状态（结果）
	TaskType       pb.PerfTaskType           `protobuf:"varint,32,opt,name=task_type,json=taskType,proto3,enum=common.PerfTaskType" json:"task_type,omitempty"`                         // 任务类型（执行、调试）
	ExecutionMode  pb.PerfTaskExecutionMode  `protobuf:"varint,33,opt,name=execution_mode,json=executionMode,proto3,enum=common.PerfTaskExecutionMode" json:"execution_mode,omitempty"` // 执行方式（按时长、按次数）
	Services       []*pb.PerfServiceMetaData `protobuf:"bytes,34,rep,name=services,proto3" json:"services,omitempty"`                                                                   // 服务的元数据
	CostTime       int64                     `protobuf:"varint,35,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`                                                  // 执行耗时（单位为毫秒）
	MonitorUrls    []*MonitorUrl             `protobuf:"bytes,36,rep,name=monitor_urls,json=monitorUrls,proto3" json:"monitor_urls,omitempty"`                                          // 监控面板地址
	ExecutedBy     string                    `protobuf:"bytes,37,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"`                                             // 执行者
	StartedAt      int64                     `protobuf:"varint,38,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`                                               // 开始时间
	EndedAt        int64                     `protobuf:"varint,39,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`                                                     // 结束时间
	ApiMetrics     []*APIMetric              `protobuf:"bytes,51,rep,name=api_metrics,json=apiMetrics,proto3" json:"api_metrics,omitempty"`                                             // 接口指标信息
	ErrMsg         *ErrorMessage             `protobuf:"bytes,52,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`                                                         // 错误信息
	CreatedBy      string                    `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                                // 创建者
	UpdatedBy      string                    `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                                // 更新者
	CreatedAt      int64                     `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                               // 创建时间
	UpdatedAt      int64                     `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                               // 更新时间
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PerfPlanRecord) Reset() {
	*x = PerfPlanRecord{}
	mi := &file_reporter_perfreporter_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfPlanRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfPlanRecord) ProtoMessage() {}

func (x *PerfPlanRecord) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_perfreporter_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfPlanRecord.ProtoReflect.Descriptor instead.
func (*PerfPlanRecord) Descriptor() ([]byte, []int) {
	return file_reporter_perfreporter_proto_rawDescGZIP(), []int{0}
}

func (x *PerfPlanRecord) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *PerfPlanRecord) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *PerfPlanRecord) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfPlanRecord) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *PerfPlanRecord) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

func (x *PerfPlanRecord) GetTriggerMode() pb.TriggerMode {
	if x != nil {
		return x.TriggerMode
	}
	return pb.TriggerMode(0)
}

func (x *PerfPlanRecord) GetTargetMaxRps() int64 {
	if x != nil {
		return x.TargetMaxRps
	}
	return 0
}

func (x *PerfPlanRecord) GetTargetDuration() uint32 {
	if x != nil {
		return x.TargetDuration
	}
	return 0
}

func (x *PerfPlanRecord) GetProtocol() pb.Protocol {
	if x != nil {
		return x.Protocol
	}
	return pb.Protocol(0)
}

func (x *PerfPlanRecord) GetTargetEnv() pb.TargetEnvironment {
	if x != nil {
		return x.TargetEnv
	}
	return pb.TargetEnvironment(0)
}

func (x *PerfPlanRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PerfPlanRecord) GetTaskType() pb.PerfTaskType {
	if x != nil {
		return x.TaskType
	}
	return pb.PerfTaskType(0)
}

func (x *PerfPlanRecord) GetExecutionMode() pb.PerfTaskExecutionMode {
	if x != nil {
		return x.ExecutionMode
	}
	return pb.PerfTaskExecutionMode(0)
}

func (x *PerfPlanRecord) GetServices() []*pb.PerfServiceMetaData {
	if x != nil {
		return x.Services
	}
	return nil
}

func (x *PerfPlanRecord) GetCostTime() int64 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *PerfPlanRecord) GetMonitorUrls() []*MonitorUrl {
	if x != nil {
		return x.MonitorUrls
	}
	return nil
}

func (x *PerfPlanRecord) GetExecutedBy() string {
	if x != nil {
		return x.ExecutedBy
	}
	return ""
}

func (x *PerfPlanRecord) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *PerfPlanRecord) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *PerfPlanRecord) GetApiMetrics() []*APIMetric {
	if x != nil {
		return x.ApiMetrics
	}
	return nil
}

func (x *PerfPlanRecord) GetErrMsg() *ErrorMessage {
	if x != nil {
		return x.ErrMsg
	}
	return nil
}

func (x *PerfPlanRecord) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *PerfPlanRecord) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *PerfPlanRecord) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PerfPlanRecord) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type PerfSuiteRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TaskId        string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                        // 任务ID
	ExecuteId     string                 `protobuf:"bytes,2,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`               // 压测集合执行ID
	PlanExecuteId string                 `protobuf:"bytes,4,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"` // 压测计划执行ID
	ProjectId     string                 `protobuf:"bytes,11,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`              // 项目ID
	SuiteId       string                 `protobuf:"bytes,12,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`                    // 集合ID
	SuiteName     string                 `protobuf:"bytes,13,opt,name=suite_name,json=suiteName,proto3" json:"suite_name,omitempty"`              // 集合名称
	Status        string                 `protobuf:"bytes,31,opt,name=status,proto3" json:"status,omitempty"`                                     // 执行状态（结果）
	CostTime      int64                  `protobuf:"varint,32,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`                // 执行耗时（单位为毫秒）
	ExecutedBy    string                 `protobuf:"bytes,33,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"`           // 执行者
	StartedAt     int64                  `protobuf:"varint,34,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`             // 开始时间
	EndedAt       int64                  `protobuf:"varint,35,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`                   // 结束时间
	ErrMsg        *ErrorMessage          `protobuf:"bytes,52,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`                       // 错误信息
	CreatedBy     string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`              // 创建者
	UpdatedBy     string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`              // 更新者
	CreatedAt     int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`             // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`             // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfSuiteRecord) Reset() {
	*x = PerfSuiteRecord{}
	mi := &file_reporter_perfreporter_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfSuiteRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfSuiteRecord) ProtoMessage() {}

func (x *PerfSuiteRecord) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_perfreporter_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfSuiteRecord.ProtoReflect.Descriptor instead.
func (*PerfSuiteRecord) Descriptor() ([]byte, []int) {
	return file_reporter_perfreporter_proto_rawDescGZIP(), []int{1}
}

func (x *PerfSuiteRecord) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *PerfSuiteRecord) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *PerfSuiteRecord) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *PerfSuiteRecord) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfSuiteRecord) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *PerfSuiteRecord) GetSuiteName() string {
	if x != nil {
		return x.SuiteName
	}
	return ""
}

func (x *PerfSuiteRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PerfSuiteRecord) GetCostTime() int64 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *PerfSuiteRecord) GetExecutedBy() string {
	if x != nil {
		return x.ExecutedBy
	}
	return ""
}

func (x *PerfSuiteRecord) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *PerfSuiteRecord) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *PerfSuiteRecord) GetErrMsg() *ErrorMessage {
	if x != nil {
		return x.ErrMsg
	}
	return nil
}

func (x *PerfSuiteRecord) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *PerfSuiteRecord) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *PerfSuiteRecord) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PerfSuiteRecord) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type PerfCaseRecord struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	TaskId         string                 `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                           // 任务ID
	ExecuteId      string                 `protobuf:"bytes,2,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                  // 压测用例执行ID
	SuiteExecuteId string                 `protobuf:"bytes,3,opt,name=suite_execute_id,json=suiteExecuteId,proto3" json:"suite_execute_id,omitempty"` // 压测集合执行ID
	PlanExecuteId  string                 `protobuf:"bytes,4,opt,name=plan_execute_id,json=planExecuteId,proto3" json:"plan_execute_id,omitempty"`    // 压测计划执行ID
	ProjectId      string                 `protobuf:"bytes,11,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                 // 项目ID
	PlanId         string                 `protobuf:"bytes,12,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                          // 计划ID
	PlanName       string                 `protobuf:"bytes,13,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                    // 计划名称
	SuiteId        string                 `protobuf:"bytes,14,opt,name=suite_id,json=suiteId,proto3" json:"suite_id,omitempty"`                       // 集合ID
	SuiteName      string                 `protobuf:"bytes,15,opt,name=suite_name,json=suiteName,proto3" json:"suite_name,omitempty"`                 // 集合名称
	CaseId         string                 `protobuf:"bytes,16,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`                          // 用例ID
	CaseName       string                 `protobuf:"bytes,17,opt,name=case_name,json=caseName,proto3" json:"case_name,omitempty"`                    // 用例名称
	Steps          []*PerfCaseStepInfo    `protobuf:"bytes,21,rep,name=steps,proto3" json:"steps,omitempty"`                                          // 用例步骤
	PerfData       *PerfDataInfo          `protobuf:"bytes,22,opt,name=perf_data,json=perfData,proto3" json:"perf_data,omitempty"`                    // 压测数据
	LoadGenerator  *pb.LoadGenerator      `protobuf:"bytes,23,opt,name=load_generator,json=loadGenerator,proto3" json:"load_generator,omitempty"`     // 施压机资源
	Status         string                 `protobuf:"bytes,31,opt,name=status,proto3" json:"status,omitempty"`                                        // 执行状态（结果）
	CostTime       int64                  `protobuf:"varint,32,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`                   // 执行耗时（单位为毫秒）
	ExecutedBy     string                 `protobuf:"bytes,33,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"`              // 执行者
	StartedAt      int64                  `protobuf:"varint,34,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`                // 开始时间
	EndedAt        int64                  `protobuf:"varint,35,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`                      // 结束时间
	ApiMetrics     []*APIMetric           `protobuf:"bytes,51,rep,name=api_metrics,json=apiMetrics,proto3" json:"api_metrics,omitempty"`              // 接口指标信息
	ErrMsg         *ErrorMessage          `protobuf:"bytes,52,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`                          // 错误信息
	CreatedBy      string                 `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                 // 创建者
	UpdatedBy      string                 `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                 // 更新者
	CreatedAt      int64                  `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                // 创建时间
	UpdatedAt      int64                  `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                // 更新时间
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PerfCaseRecord) Reset() {
	*x = PerfCaseRecord{}
	mi := &file_reporter_perfreporter_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfCaseRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfCaseRecord) ProtoMessage() {}

func (x *PerfCaseRecord) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_perfreporter_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfCaseRecord.ProtoReflect.Descriptor instead.
func (*PerfCaseRecord) Descriptor() ([]byte, []int) {
	return file_reporter_perfreporter_proto_rawDescGZIP(), []int{2}
}

func (x *PerfCaseRecord) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *PerfCaseRecord) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *PerfCaseRecord) GetSuiteExecuteId() string {
	if x != nil {
		return x.SuiteExecuteId
	}
	return ""
}

func (x *PerfCaseRecord) GetPlanExecuteId() string {
	if x != nil {
		return x.PlanExecuteId
	}
	return ""
}

func (x *PerfCaseRecord) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfCaseRecord) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *PerfCaseRecord) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

func (x *PerfCaseRecord) GetSuiteId() string {
	if x != nil {
		return x.SuiteId
	}
	return ""
}

func (x *PerfCaseRecord) GetSuiteName() string {
	if x != nil {
		return x.SuiteName
	}
	return ""
}

func (x *PerfCaseRecord) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *PerfCaseRecord) GetCaseName() string {
	if x != nil {
		return x.CaseName
	}
	return ""
}

func (x *PerfCaseRecord) GetSteps() []*PerfCaseStepInfo {
	if x != nil {
		return x.Steps
	}
	return nil
}

func (x *PerfCaseRecord) GetPerfData() *PerfDataInfo {
	if x != nil {
		return x.PerfData
	}
	return nil
}

func (x *PerfCaseRecord) GetLoadGenerator() *pb.LoadGenerator {
	if x != nil {
		return x.LoadGenerator
	}
	return nil
}

func (x *PerfCaseRecord) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *PerfCaseRecord) GetCostTime() int64 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *PerfCaseRecord) GetExecutedBy() string {
	if x != nil {
		return x.ExecutedBy
	}
	return ""
}

func (x *PerfCaseRecord) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *PerfCaseRecord) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *PerfCaseRecord) GetApiMetrics() []*APIMetric {
	if x != nil {
		return x.ApiMetrics
	}
	return nil
}

func (x *PerfCaseRecord) GetErrMsg() *ErrorMessage {
	if x != nil {
		return x.ErrMsg
	}
	return nil
}

func (x *PerfCaseRecord) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *PerfCaseRecord) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *PerfCaseRecord) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PerfCaseRecord) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type PerfCaseStepInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                               // 步骤名称
	Type          pb.PerfCaseStepType    `protobuf:"varint,2,opt,name=type,proto3,enum=common.PerfCaseStepType" json:"type,omitempty"` // 步骤类型
	RateLimits    []*pb.RateLimitV2      `protobuf:"bytes,3,rep,name=rate_limits,json=rateLimits,proto3" json:"rate_limits,omitempty"` // 限流配置
	ApiName       string                 `protobuf:"bytes,4,opt,name=api_name,json=apiName,proto3" json:"api_name,omitempty"`          // 接口名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfCaseStepInfo) Reset() {
	*x = PerfCaseStepInfo{}
	mi := &file_reporter_perfreporter_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfCaseStepInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfCaseStepInfo) ProtoMessage() {}

func (x *PerfCaseStepInfo) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_perfreporter_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfCaseStepInfo.ProtoReflect.Descriptor instead.
func (*PerfCaseStepInfo) Descriptor() ([]byte, []int) {
	return file_reporter_perfreporter_proto_rawDescGZIP(), []int{3}
}

func (x *PerfCaseStepInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfCaseStepInfo) GetType() pb.PerfCaseStepType {
	if x != nil {
		return x.Type
	}
	return pb.PerfCaseStepType(0)
}

func (x *PerfCaseStepInfo) GetRateLimits() []*pb.RateLimitV2 {
	if x != nil {
		return x.RateLimits
	}
	return nil
}

func (x *PerfCaseStepInfo) GetApiName() string {
	if x != nil {
		return x.ApiName
	}
	return ""
}

type PerfDataInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProjectId     string                 `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`       // 项目ID
	DataId        string                 `protobuf:"bytes,2,opt,name=data_id,json=dataId,proto3" json:"data_id,omitempty"`                // 压测数据ID
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                  // 压测数据名称
	NumberOfVu    uint32                 `protobuf:"varint,4,opt,name=number_of_vu,json=numberOfVu,proto3" json:"number_of_vu,omitempty"` // 虚拟用户数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PerfDataInfo) Reset() {
	*x = PerfDataInfo{}
	mi := &file_reporter_perfreporter_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PerfDataInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PerfDataInfo) ProtoMessage() {}

func (x *PerfDataInfo) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_perfreporter_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PerfDataInfo.ProtoReflect.Descriptor instead.
func (*PerfDataInfo) Descriptor() ([]byte, []int) {
	return file_reporter_perfreporter_proto_rawDescGZIP(), []int{4}
}

func (x *PerfDataInfo) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *PerfDataInfo) GetDataId() string {
	if x != nil {
		return x.DataId
	}
	return ""
}

func (x *PerfDataInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PerfDataInfo) GetNumberOfVu() uint32 {
	if x != nil {
		return x.NumberOfVu
	}
	return 0
}

type MonitorUrl struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                             // 名称
	Type          pb.MonitorUrlType      `protobuf:"varint,2,opt,name=type,proto3,enum=common.MonitorUrlType" json:"type,omitempty"` // 类型
	Url           string                 `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`                               // 跳转地址
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MonitorUrl) Reset() {
	*x = MonitorUrl{}
	mi := &file_reporter_perfreporter_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MonitorUrl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MonitorUrl) ProtoMessage() {}

func (x *MonitorUrl) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_perfreporter_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MonitorUrl.ProtoReflect.Descriptor instead.
func (*MonitorUrl) Descriptor() ([]byte, []int) {
	return file_reporter_perfreporter_proto_rawDescGZIP(), []int{5}
}

func (x *MonitorUrl) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *MonitorUrl) GetType() pb.MonitorUrlType {
	if x != nil {
		return x.Type
	}
	return pb.MonitorUrlType(0)
}

func (x *MonitorUrl) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type APIMetric struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	ApiName       string                      `protobuf:"bytes,1,opt,name=api_name,json=apiName,proto3" json:"api_name,omitempty"`                    // 接口名称
	ReqSuccessful int64                       `protobuf:"varint,2,opt,name=req_successful,json=reqSuccessful,proto3" json:"req_successful,omitempty"` // 请求成功次数
	ReqFailed     int64                       `protobuf:"varint,3,opt,name=req_failed,json=reqFailed,proto3" json:"req_failed,omitempty"`             // 请求失败次数
	RespSuites    []*APIMetric_ResponseResult `protobuf:"bytes,4,rep,name=resp_suites,json=respSuites,proto3" json:"resp_suites,omitempty"`           // 响应结果集
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *APIMetric) Reset() {
	*x = APIMetric{}
	mi := &file_reporter_perfreporter_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *APIMetric) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APIMetric) ProtoMessage() {}

func (x *APIMetric) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_perfreporter_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APIMetric.ProtoReflect.Descriptor instead.
func (*APIMetric) Descriptor() ([]byte, []int) {
	return file_reporter_perfreporter_proto_rawDescGZIP(), []int{6}
}

func (x *APIMetric) GetApiName() string {
	if x != nil {
		return x.ApiName
	}
	return ""
}

func (x *APIMetric) GetReqSuccessful() int64 {
	if x != nil {
		return x.ReqSuccessful
	}
	return 0
}

func (x *APIMetric) GetReqFailed() int64 {
	if x != nil {
		return x.ReqFailed
	}
	return 0
}

func (x *APIMetric) GetRespSuites() []*APIMetric_ResponseResult {
	if x != nil {
		return x.RespSuites
	}
	return nil
}

type ErrorMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`                           // 错误码
	MessageEn     string                 `protobuf:"bytes,2,opt,name=message_en,json=messageEn,proto3" json:"message_en,omitempty"` // 英文错误信息
	MessageZh     string                 `protobuf:"bytes,3,opt,name=message_zh,json=messageZh,proto3" json:"message_zh,omitempty"` // 中文错误信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorMessage) Reset() {
	*x = ErrorMessage{}
	mi := &file_reporter_perfreporter_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorMessage) ProtoMessage() {}

func (x *ErrorMessage) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_perfreporter_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorMessage.ProtoReflect.Descriptor instead.
func (*ErrorMessage) Descriptor() ([]byte, []int) {
	return file_reporter_perfreporter_proto_rawDescGZIP(), []int{7}
}

func (x *ErrorMessage) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ErrorMessage) GetMessageEn() string {
	if x != nil {
		return x.MessageEn
	}
	return ""
}

func (x *ErrorMessage) GetMessageZh() string {
	if x != nil {
		return x.MessageZh
	}
	return ""
}

type SearchPerfPlanRecordItem struct {
	state          protoimpl.MessageState   `protogen:"open.v1"`
	TaskId         string                   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`                                                          // 任务ID
	ExecuteId      string                   `protobuf:"bytes,2,opt,name=execute_id,json=executeId,proto3" json:"execute_id,omitempty"`                                                 // 压测计划执行ID
	ProjectId      string                   `protobuf:"bytes,11,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`                                                // 项目ID
	PlanId         string                   `protobuf:"bytes,12,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                                         // 计划ID
	PlanName       string                   `protobuf:"bytes,13,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                                                   // 计划名称
	TriggerMode    pb.TriggerMode           `protobuf:"varint,14,opt,name=trigger_mode,json=triggerMode,proto3,enum=common.TriggerMode" json:"trigger_mode,omitempty"`                 // 触发方式（手动、定时、接口）
	TargetMaxRps   int64                    `protobuf:"varint,15,opt,name=target_max_rps,json=targetMaxRps,proto3" json:"target_max_rps,omitempty"`                                    // 目标最大的RPS
	TargetDuration uint32                   `protobuf:"varint,16,opt,name=target_duration,json=targetDuration,proto3" json:"target_duration,omitempty"`                                // 目标压测持续时长（单位为秒）
	Protocol       pb.Protocol              `protobuf:"varint,17,opt,name=protocol,proto3,enum=common.Protocol" json:"protocol,omitempty"`                                             // 协议
	TargetEnv      pb.TargetEnvironment     `protobuf:"varint,18,opt,name=target_env,json=targetEnv,proto3,enum=common.TargetEnvironment" json:"target_env,omitempty"`                 // 目标环境
	Status         string                   `protobuf:"bytes,31,opt,name=status,proto3" json:"status,omitempty"`                                                                       // 执行状态（结果）
	TaskType       pb.PerfTaskType          `protobuf:"varint,32,opt,name=task_type,json=taskType,proto3,enum=common.PerfTaskType" json:"task_type,omitempty"`                         // 任务类型（执行、调试）
	ExecutionMode  pb.PerfTaskExecutionMode `protobuf:"varint,33,opt,name=execution_mode,json=executionMode,proto3,enum=common.PerfTaskExecutionMode" json:"execution_mode,omitempty"` // 执行方式（按时长、按次数）
	CostTime       int64                    `protobuf:"varint,34,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`                                                  // 执行耗时（单位为毫秒）
	HasMonitorUrl  bool                     `protobuf:"varint,35,opt,name=has_monitor_url,json=hasMonitorUrl,proto3" json:"has_monitor_url,omitempty"`                                 // 监控面板地址是否非空
	ExecutedBy     string                   `protobuf:"bytes,36,opt,name=executed_by,json=executedBy,proto3" json:"executed_by,omitempty"`                                             // 执行者
	StartedAt      int64                    `protobuf:"varint,37,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`                                               // 开始时间
	EndedAt        int64                    `protobuf:"varint,38,opt,name=ended_at,json=endedAt,proto3" json:"ended_at,omitempty"`                                                     // 结束时间
	ErrMsg         *ErrorMessage            `protobuf:"bytes,39,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`                                                         // 错误信息
	CreatedBy      string                   `protobuf:"bytes,96,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`                                                // 创建者
	UpdatedBy      string                   `protobuf:"bytes,97,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`                                                // 更新者
	CreatedAt      int64                    `protobuf:"varint,98,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                               // 创建时间
	UpdatedAt      int64                    `protobuf:"varint,99,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                               // 更新时间
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SearchPerfPlanRecordItem) Reset() {
	*x = SearchPerfPlanRecordItem{}
	mi := &file_reporter_perfreporter_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPerfPlanRecordItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPerfPlanRecordItem) ProtoMessage() {}

func (x *SearchPerfPlanRecordItem) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_perfreporter_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPerfPlanRecordItem.ProtoReflect.Descriptor instead.
func (*SearchPerfPlanRecordItem) Descriptor() ([]byte, []int) {
	return file_reporter_perfreporter_proto_rawDescGZIP(), []int{8}
}

func (x *SearchPerfPlanRecordItem) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *SearchPerfPlanRecordItem) GetExecuteId() string {
	if x != nil {
		return x.ExecuteId
	}
	return ""
}

func (x *SearchPerfPlanRecordItem) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *SearchPerfPlanRecordItem) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *SearchPerfPlanRecordItem) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

func (x *SearchPerfPlanRecordItem) GetTriggerMode() pb.TriggerMode {
	if x != nil {
		return x.TriggerMode
	}
	return pb.TriggerMode(0)
}

func (x *SearchPerfPlanRecordItem) GetTargetMaxRps() int64 {
	if x != nil {
		return x.TargetMaxRps
	}
	return 0
}

func (x *SearchPerfPlanRecordItem) GetTargetDuration() uint32 {
	if x != nil {
		return x.TargetDuration
	}
	return 0
}

func (x *SearchPerfPlanRecordItem) GetProtocol() pb.Protocol {
	if x != nil {
		return x.Protocol
	}
	return pb.Protocol(0)
}

func (x *SearchPerfPlanRecordItem) GetTargetEnv() pb.TargetEnvironment {
	if x != nil {
		return x.TargetEnv
	}
	return pb.TargetEnvironment(0)
}

func (x *SearchPerfPlanRecordItem) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SearchPerfPlanRecordItem) GetTaskType() pb.PerfTaskType {
	if x != nil {
		return x.TaskType
	}
	return pb.PerfTaskType(0)
}

func (x *SearchPerfPlanRecordItem) GetExecutionMode() pb.PerfTaskExecutionMode {
	if x != nil {
		return x.ExecutionMode
	}
	return pb.PerfTaskExecutionMode(0)
}

func (x *SearchPerfPlanRecordItem) GetCostTime() int64 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *SearchPerfPlanRecordItem) GetHasMonitorUrl() bool {
	if x != nil {
		return x.HasMonitorUrl
	}
	return false
}

func (x *SearchPerfPlanRecordItem) GetExecutedBy() string {
	if x != nil {
		return x.ExecutedBy
	}
	return ""
}

func (x *SearchPerfPlanRecordItem) GetStartedAt() int64 {
	if x != nil {
		return x.StartedAt
	}
	return 0
}

func (x *SearchPerfPlanRecordItem) GetEndedAt() int64 {
	if x != nil {
		return x.EndedAt
	}
	return 0
}

func (x *SearchPerfPlanRecordItem) GetErrMsg() *ErrorMessage {
	if x != nil {
		return x.ErrMsg
	}
	return nil
}

func (x *SearchPerfPlanRecordItem) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *SearchPerfPlanRecordItem) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

func (x *SearchPerfPlanRecordItem) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *SearchPerfPlanRecordItem) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

type APIMetric_ResponseResult struct {
	state         protoimpl.MessageState                   `protogen:"open.v1"`
	Result        string                                   `protobuf:"bytes,1,opt,name=result,proto3" json:"result,omitempty"`       // 响应结果
	Count         uint64                                   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`        // 响应总数（样本总数）
	Sum           float64                                  `protobuf:"fixed64,3,opt,name=sum,proto3" json:"sum,omitempty"`           // 总响应耗时（单位为毫秒）（样本值的大小总和）
	Quantiles     []*APIMetric_ResponseResult_KeyValuePair `protobuf:"bytes,4,rep,name=quantiles,proto3" json:"quantiles,omitempty"` // 响应耗时百分位统计数据
	Buckets       []*APIMetric_ResponseResult_KeyValuePair `protobuf:"bytes,5,rep,name=buckets,proto3" json:"buckets,omitempty"`     // 响应耗时分布统计数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *APIMetric_ResponseResult) Reset() {
	*x = APIMetric_ResponseResult{}
	mi := &file_reporter_perfreporter_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *APIMetric_ResponseResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APIMetric_ResponseResult) ProtoMessage() {}

func (x *APIMetric_ResponseResult) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_perfreporter_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APIMetric_ResponseResult.ProtoReflect.Descriptor instead.
func (*APIMetric_ResponseResult) Descriptor() ([]byte, []int) {
	return file_reporter_perfreporter_proto_rawDescGZIP(), []int{6, 0}
}

func (x *APIMetric_ResponseResult) GetResult() string {
	if x != nil {
		return x.Result
	}
	return ""
}

func (x *APIMetric_ResponseResult) GetCount() uint64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *APIMetric_ResponseResult) GetSum() float64 {
	if x != nil {
		return x.Sum
	}
	return 0
}

func (x *APIMetric_ResponseResult) GetQuantiles() []*APIMetric_ResponseResult_KeyValuePair {
	if x != nil {
		return x.Quantiles
	}
	return nil
}

func (x *APIMetric_ResponseResult) GetBuckets() []*APIMetric_ResponseResult_KeyValuePair {
	if x != nil {
		return x.Buckets
	}
	return nil
}

type APIMetric_ResponseResult_KeyValuePair struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`     // 键
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"` // 值
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *APIMetric_ResponseResult_KeyValuePair) Reset() {
	*x = APIMetric_ResponseResult_KeyValuePair{}
	mi := &file_reporter_perfreporter_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *APIMetric_ResponseResult_KeyValuePair) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*APIMetric_ResponseResult_KeyValuePair) ProtoMessage() {}

func (x *APIMetric_ResponseResult_KeyValuePair) ProtoReflect() protoreflect.Message {
	mi := &file_reporter_perfreporter_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use APIMetric_ResponseResult_KeyValuePair.ProtoReflect.Descriptor instead.
func (*APIMetric_ResponseResult_KeyValuePair) Descriptor() ([]byte, []int) {
	return file_reporter_perfreporter_proto_rawDescGZIP(), []int{6, 0, 0}
}

func (x *APIMetric_ResponseResult_KeyValuePair) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *APIMetric_ResponseResult_KeyValuePair) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_reporter_perfreporter_proto protoreflect.FileDescriptor

var file_reporter_perfreporter_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2f, 0x70, 0x65, 0x72, 0x66, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x70, 0x65, 0x72, 0x66, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xea, 0x07,
	0x0a, 0x0e, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a,
	0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65,
	0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x6d, 0x61, 0x78, 0x5f, 0x72, 0x70, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x4d, 0x61, 0x78, 0x52, 0x70, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63,
	0x6f, 0x6c, 0x12, 0x38, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x65, 0x6e, 0x76,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x6e, 0x76, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x31, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x65, 0x72, 0x66, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x74,
	0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x0e, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x54, 0x61, 0x73,
	0x6b, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0d,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x37, 0x0a,
	0x08, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x22, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x23, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6f, 0x73, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x0c, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x75,
	0x72, 0x6c, 0x73, 0x18, 0x24, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x72, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c, 0x52,
	0x0b, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x25, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x26, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x27, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x12, 0x34, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x5f, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x33, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2e, 0x41, 0x50, 0x49, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x2f, 0x0a,
	0x07, 0x65, 0x72, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x34, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x87, 0x04, 0x0a, 0x0f, 0x50,
	0x65, 0x72, 0x66, 0x53, 0x75, 0x69, 0x74, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x17,
	0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x65,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x75, 0x69, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x69, 0x74,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75,
	0x69, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x20, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x63, 0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x21, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x22, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08,
	0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x23, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x12, 0x2f, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x5f, 0x6d,
	0x73, 0x67, 0x18, 0x34, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x72, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x22, 0xf7, 0x06, 0x0a, 0x0e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12,
	0x28, 0x0a, 0x10, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x69, 0x74, 0x65,
	0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x70, 0x6c, 0x61,
	0x6e, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c, 0x61, 0x6e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64,
	0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c,
	0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x69, 0x74, 0x65, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x69, 0x74, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x69, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x63, 0x61, 0x73, 0x65, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x61, 0x73,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x61,
	0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x18,
	0x15, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72,
	0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x05, 0x73, 0x74, 0x65, 0x70, 0x73, 0x12, 0x33, 0x0a, 0x09, 0x70, 0x65, 0x72, 0x66,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x70, 0x65, 0x72, 0x66, 0x44, 0x61, 0x74, 0x61, 0x12, 0x3c, 0x0a,
	0x0e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18,
	0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4c,
	0x6f, 0x61, 0x64, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x0d, 0x6c, 0x6f,
	0x61, 0x64, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x20, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x22, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x23, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74, 0x12, 0x34, 0x0a, 0x0b, 0x61,
	0x70, 0x69, 0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x33, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x13, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2e, 0x41, 0x50, 0x49, 0x4d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x12, 0x2f, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x34, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d,
	0x73, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79,
	0x18, 0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42,
	0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x61, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x62,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x63, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xd5,
	0x01, 0x0a, 0x10, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61, 0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x36, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x43, 0x61,
	0x73, 0x65, 0x53, 0x74, 0x65, 0x70, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x45, 0x0a, 0x0b, 0x72, 0x61,
	0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x52, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x56, 0x32, 0x42, 0x0f, 0xfa, 0x42, 0x0c, 0x92, 0x01, 0x09, 0x08, 0x01, 0x22, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x72, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x73, 0x12, 0x23, 0x0a, 0x08, 0x61, 0x70, 0x69, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18, 0xff, 0x01, 0x52, 0x07, 0x61,
	0x70, 0x69, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xd2, 0x01, 0x0a, 0x0c, 0x50, 0x65, 0x72, 0x66, 0x44,
	0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1e, 0xfa, 0x42, 0x1b,
	0x72, 0x19, 0x32, 0x17, 0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f,
	0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x7c, 0x5e, 0x31, 0x24, 0x29, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x07, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x1f, 0xfa, 0x42, 0x1c, 0x72, 0x1a, 0x32, 0x15,
	0x28, 0x3f, 0x3a, 0x5e, 0x70, 0x65, 0x72, 0x66, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x64,
	0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0xd0, 0x01, 0x01, 0x52, 0x06, 0x64, 0x61, 0x74, 0x61, 0x49, 0x64,
	0x12, 0x1e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x72, 0x05, 0x18, 0x40, 0xd0, 0x01, 0x01, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x29, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x76, 0x75,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x20, 0x00, 0x52,
	0x0a, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x4f, 0x66, 0x56, 0x75, 0x22, 0x5e, 0x0a, 0x0a, 0x4d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x55, 0x72, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0xd6, 0x03, 0x0a, 0x09,
	0x41, 0x50, 0x49, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x69,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x69,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x5f, 0x73, 0x75, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x72, 0x65,
	0x71, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x72,
	0x65, 0x71, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x72, 0x65, 0x71, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x12, 0x43, 0x0a, 0x0b, 0x72, 0x65,
	0x73, 0x70, 0x5f, 0x73, 0x75, 0x69, 0x74, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2e, 0x41, 0x50, 0x49, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x53, 0x75, 0x69, 0x74, 0x65, 0x73, 0x1a,
	0xa2, 0x02, 0x0a, 0x0e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x73, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x73,
	0x75, 0x6d, 0x12, 0x4d, 0x0a, 0x09, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x6c, 0x65, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72,
	0x2e, 0x41, 0x50, 0x49, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x52, 0x09, 0x71, 0x75, 0x61, 0x6e, 0x74, 0x69, 0x6c, 0x65,
	0x73, 0x12, 0x49, 0x0a, 0x07, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2e, 0x41, 0x50,
	0x49, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x2e, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50,
	0x61, 0x69, 0x72, 0x52, 0x07, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x1a, 0x36, 0x0a, 0x0c,
	0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x22, 0x60, 0x0a, 0x0c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x5f, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x7a, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x5a, 0x68, 0x22, 0xf4, 0x06, 0x0a, 0x18, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x50, 0x65, 0x72, 0x66, 0x50, 0x6c, 0x61, 0x6e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c,
	0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61,
	0x6e, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x36, 0x0a, 0x0c, 0x74, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x72, 0x69, 0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x0b, 0x74, 0x72, 0x69,
	0x67, 0x67, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x70, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4d, 0x61, 0x78, 0x52, 0x70, 0x73, 0x12, 0x27,
	0x0a, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x10, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x38, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x65, 0x6e, 0x76, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x6e, 0x76, 0x69, 0x72, 0x6f, 0x6e,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x6e, 0x76, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x31, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x44, 0x0a, 0x0e, 0x65, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x21, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x65, 0x72, 0x66,
	0x54, 0x61, 0x73, 0x6b, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64,
	0x65, 0x52, 0x0d, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x6f, 0x64, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x22, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x08, 0x63, 0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a,
	0x0f, 0x68, 0x61, 0x73, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x75, 0x72, 0x6c,
	0x18, 0x23, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x61, 0x73, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x65,
	0x64, 0x5f, 0x62, 0x79, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x74, 0x18, 0x25, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x26, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x2f, 0x0a, 0x07, 0x65, 0x72, 0x72, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x27, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2e, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x06, 0x65, 0x72, 0x72, 0x4d, 0x73,
	0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18,
	0x60, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x62, 0x79, 0x18, 0x61,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12,
	0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x62, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x63, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x42, 0x5a,
	0x40, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73, 0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f, 0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x2f, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x72, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70,
	0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_reporter_perfreporter_proto_rawDescOnce sync.Once
	file_reporter_perfreporter_proto_rawDescData = file_reporter_perfreporter_proto_rawDesc
)

func file_reporter_perfreporter_proto_rawDescGZIP() []byte {
	file_reporter_perfreporter_proto_rawDescOnce.Do(func() {
		file_reporter_perfreporter_proto_rawDescData = protoimpl.X.CompressGZIP(file_reporter_perfreporter_proto_rawDescData)
	})
	return file_reporter_perfreporter_proto_rawDescData
}

var file_reporter_perfreporter_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_reporter_perfreporter_proto_goTypes = []any{
	(*PerfPlanRecord)(nil),                        // 0: reporter.PerfPlanRecord
	(*PerfSuiteRecord)(nil),                       // 1: reporter.PerfSuiteRecord
	(*PerfCaseRecord)(nil),                        // 2: reporter.PerfCaseRecord
	(*PerfCaseStepInfo)(nil),                      // 3: reporter.PerfCaseStepInfo
	(*PerfDataInfo)(nil),                          // 4: reporter.PerfDataInfo
	(*MonitorUrl)(nil),                            // 5: reporter.MonitorUrl
	(*APIMetric)(nil),                             // 6: reporter.APIMetric
	(*ErrorMessage)(nil),                          // 7: reporter.ErrorMessage
	(*SearchPerfPlanRecordItem)(nil),              // 8: reporter.SearchPerfPlanRecordItem
	(*APIMetric_ResponseResult)(nil),              // 9: reporter.APIMetric.ResponseResult
	(*APIMetric_ResponseResult_KeyValuePair)(nil), // 10: reporter.APIMetric.ResponseResult.KeyValuePair
	(pb.TriggerMode)(0),                           // 11: common.TriggerMode
	(pb.Protocol)(0),                              // 12: common.Protocol
	(pb.TargetEnvironment)(0),                     // 13: common.TargetEnvironment
	(pb.PerfTaskType)(0),                          // 14: common.PerfTaskType
	(pb.PerfTaskExecutionMode)(0),                 // 15: common.PerfTaskExecutionMode
	(*pb.PerfServiceMetaData)(nil),                // 16: common.PerfServiceMetaData
	(*pb.LoadGenerator)(nil),                      // 17: common.LoadGenerator
	(pb.PerfCaseStepType)(0),                      // 18: common.PerfCaseStepType
	(*pb.RateLimitV2)(nil),                        // 19: common.RateLimitV2
	(pb.MonitorUrlType)(0),                        // 20: common.MonitorUrlType
}
var file_reporter_perfreporter_proto_depIdxs = []int32{
	11, // 0: reporter.PerfPlanRecord.trigger_mode:type_name -> common.TriggerMode
	12, // 1: reporter.PerfPlanRecord.protocol:type_name -> common.Protocol
	13, // 2: reporter.PerfPlanRecord.target_env:type_name -> common.TargetEnvironment
	14, // 3: reporter.PerfPlanRecord.task_type:type_name -> common.PerfTaskType
	15, // 4: reporter.PerfPlanRecord.execution_mode:type_name -> common.PerfTaskExecutionMode
	16, // 5: reporter.PerfPlanRecord.services:type_name -> common.PerfServiceMetaData
	5,  // 6: reporter.PerfPlanRecord.monitor_urls:type_name -> reporter.MonitorUrl
	6,  // 7: reporter.PerfPlanRecord.api_metrics:type_name -> reporter.APIMetric
	7,  // 8: reporter.PerfPlanRecord.err_msg:type_name -> reporter.ErrorMessage
	7,  // 9: reporter.PerfSuiteRecord.err_msg:type_name -> reporter.ErrorMessage
	3,  // 10: reporter.PerfCaseRecord.steps:type_name -> reporter.PerfCaseStepInfo
	4,  // 11: reporter.PerfCaseRecord.perf_data:type_name -> reporter.PerfDataInfo
	17, // 12: reporter.PerfCaseRecord.load_generator:type_name -> common.LoadGenerator
	6,  // 13: reporter.PerfCaseRecord.api_metrics:type_name -> reporter.APIMetric
	7,  // 14: reporter.PerfCaseRecord.err_msg:type_name -> reporter.ErrorMessage
	18, // 15: reporter.PerfCaseStepInfo.type:type_name -> common.PerfCaseStepType
	19, // 16: reporter.PerfCaseStepInfo.rate_limits:type_name -> common.RateLimitV2
	20, // 17: reporter.MonitorUrl.type:type_name -> common.MonitorUrlType
	9,  // 18: reporter.APIMetric.resp_suites:type_name -> reporter.APIMetric.ResponseResult
	11, // 19: reporter.SearchPerfPlanRecordItem.trigger_mode:type_name -> common.TriggerMode
	12, // 20: reporter.SearchPerfPlanRecordItem.protocol:type_name -> common.Protocol
	13, // 21: reporter.SearchPerfPlanRecordItem.target_env:type_name -> common.TargetEnvironment
	14, // 22: reporter.SearchPerfPlanRecordItem.task_type:type_name -> common.PerfTaskType
	15, // 23: reporter.SearchPerfPlanRecordItem.execution_mode:type_name -> common.PerfTaskExecutionMode
	7,  // 24: reporter.SearchPerfPlanRecordItem.err_msg:type_name -> reporter.ErrorMessage
	10, // 25: reporter.APIMetric.ResponseResult.quantiles:type_name -> reporter.APIMetric.ResponseResult.KeyValuePair
	10, // 26: reporter.APIMetric.ResponseResult.buckets:type_name -> reporter.APIMetric.ResponseResult.KeyValuePair
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_reporter_perfreporter_proto_init() }
func file_reporter_perfreporter_proto_init() {
	if File_reporter_perfreporter_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_reporter_perfreporter_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_reporter_perfreporter_proto_goTypes,
		DependencyIndexes: file_reporter_perfreporter_proto_depIdxs,
		MessageInfos:      file_reporter_perfreporter_proto_msgTypes,
	}.Build()
	File_reporter_perfreporter_proto = out.File
	file_reporter_perfreporter_proto_rawDesc = nil
	file_reporter_perfreporter_proto_goTypes = nil
	file_reporter_perfreporter_proto_depIdxs = nil
}

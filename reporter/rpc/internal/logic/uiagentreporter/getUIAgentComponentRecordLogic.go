package uiagentreporterlogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type GetUIAgentComponentRecordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetUIAgentComponentRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUIAgentComponentRecordLogic {
	return &GetUIAgentComponentRecordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// GetUIAgentComponentRecord 获取`UI Agent`组件执行记录
func (l *GetUIAgentComponentRecordLogic) GetUIAgentComponentRecord(in *pb.GetUIAgentComponentRecordReq) (out *pb.GetUIAgentComponentRecordResp, err error) {
	// todo: add your logic here and delete this line

	return &pb.GetUIAgentComponentRecordResp{}, nil
}

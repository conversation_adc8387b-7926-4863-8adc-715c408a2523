package uiagentreporterlogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/logic"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []qetutils.TypeConverter{
			commonpb.StringToTriggerMode(),
			commonpb.StringToProtocol(),
			commonpb.StringToTargetEnvironment(),
			commonpb.StringToPerfTaskType(),
			commonpb.StringToPerfTaskExecutionMode(),
			commonpb.StringToPerfCaseStepType(),
			commonpb.StringToMonitorUrlType(),
			logic.SQLNullStringToPerfServiceMetaDatas(),
			logic.SQLNullStringToMonitorUrls(),
			logic.SQLNullStringToPerfCaseSteps(),
			logic.SQLNullStringToPerfDataInfo(),
			logic.SQLNullStringToLoadGenerator(),
			logic.SQLNullStringToAPIMetrics(),
			logic.SQLNullStringToErrorMessage(),
		},
	}
}

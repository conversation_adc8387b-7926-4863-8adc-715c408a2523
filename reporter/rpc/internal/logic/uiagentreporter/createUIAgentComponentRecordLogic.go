package uiagentreporterlogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type CreateUIAgentComponentRecordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateUIAgentComponentRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateUIAgentComponentRecordLogic {
	return &CreateUIAgentComponentRecordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// CreateUIAgentComponentRecord 创建`UI Agent`组件执行记录
func (l *CreateUIAgentComponentRecordLogic) CreateUIAgentComponentRecord(in *pb.CreateUIAgentComponentRecordReq) (out *pb.CreateUIAgentComponentRecordResp, err error) {
	// todo: add your logic here and delete this line

	return &pb.CreateUIAgentComponentRecordResp{}, nil
}

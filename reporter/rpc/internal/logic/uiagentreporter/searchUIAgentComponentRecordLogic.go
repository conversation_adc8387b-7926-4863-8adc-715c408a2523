package uiagentreporterlogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type SearchUIAgentComponentRecordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSearchUIAgentComponentRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchUIAgentComponentRecordLogic {
	return &SearchUIAgentComponentRecordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// SearchUIAgentComponentRecord 搜索`UI Agent`组件执行记录
func (l *SearchUIAgentComponentRecordLogic) SearchUIAgentComponentRecord(in *pb.SearchUIAgentComponentRecordReq) (out *pb.SearchUIAgentComponentRecordResp, err error) {
	// todo: add your logic here and delete this line

	return &pb.SearchUIAgentComponentRecordResp{}, nil
}

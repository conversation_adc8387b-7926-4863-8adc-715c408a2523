package uiagentreporterlogic

import (
	"context"

	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type ModifyUIAgentComponentRecordLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewModifyUIAgentComponentRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ModifyUIAgentComponentRecordLogic {
	return &ModifyUIAgentComponentRecordLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// ModifyUIAgentComponentRecord 修改`UI Agent`组件执行记录
func (l *ModifyUIAgentComponentRecordLogic) ModifyUIAgentComponentRecord(in *pb.ModifyUIAgentComponentRecordReq) (out *pb.ModifyUIAgentComponentRecordResp, err error) {
	// todo: add your logic here and delete this line

	return &pb.ModifyUIAgentComponentRecordResp{}, nil
}

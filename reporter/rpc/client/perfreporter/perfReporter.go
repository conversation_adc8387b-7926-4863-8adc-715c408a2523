// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: reporter.proto

package perfreporter

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type (
	CaseFailForPlanStatForMq                                      = pb.CaseFailForPlanStatForMq
	CleanConfig                                                   = pb.CleanConfig
	CleanConfigs                                                  = pb.CleanConfigs
	CountFailedCaseInLastNDaysReq                                 = pb.CountFailedCaseInLastNDaysReq
	CountFailedCaseInLastNDaysResp                                = pb.CountFailedCaseInLastNDaysResp
	CreateInterfaceRecordResponse                                 = pb.CreateInterfaceRecordResponse
	CreatePerfCaseRecordReq                                       = pb.CreatePerfCaseRecordReq
	CreatePerfCaseRecordResp                                      = pb.CreatePerfCaseRecordResp
	CreatePerfPlanRecordReq                                       = pb.CreatePerfPlanRecordReq
	CreatePerfPlanRecordResp                                      = pb.CreatePerfPlanRecordResp
	CreatePerfSuiteRecordReq                                      = pb.CreatePerfSuiteRecordReq
	CreatePerfSuiteRecordResp                                     = pb.CreatePerfSuiteRecordResp
	CreatePlanRecordResponse                                      = pb.CreatePlanRecordResponse
	CreateRecordResponse                                          = pb.CreateRecordResponse
	CreateServiceRecordResponse                                   = pb.CreateServiceRecordResponse
	CreateStabilityDeviceRecordResp                               = pb.CreateStabilityDeviceRecordResp
	CreateStabilityPlanRecordResp                                 = pb.CreateStabilityPlanRecordResp
	CreateSuiteRecordResponse                                     = pb.CreateSuiteRecordResponse
	CreateUIAgentComponentRecordReq                               = pb.CreateUIAgentComponentRecordReq
	CreateUIAgentComponentRecordResp                              = pb.CreateUIAgentComponentRecordResp
	CreateUICaseRecordResponse                                    = pb.CreateUICaseRecordResponse
	CreateUIPlanRecordResponse                                    = pb.CreateUIPlanRecordResponse
	CreateUISuiteRecordResponse                                   = pb.CreateUISuiteRecordResponse
	DelCaseFailStatForPlanReq                                     = pb.DelCaseFailStatForPlanReq
	DelCaseFailStatForPlanResp                                    = pb.DelCaseFailStatForPlanResp
	FindRedundantPlanRecordResp                                   = pb.FindRedundantPlanRecordResp
	GetCaseLatestRecordRequest                                    = pb.GetCaseLatestRecordRequest
	GetCaseLatestRecordResponse                                   = pb.GetCaseLatestRecordResponse
	GetCaseLatestRecordResponse_RecordCaseRecord                  = pb.GetCaseLatestRecordResponse_RecordCaseRecord
	GetChildrenRecordRequest                                      = pb.GetChildrenRecordRequest
	GetChildrenRecordResponse                                     = pb.GetChildrenRecordResponse
	GetChildrenRecordResponse_ChildRecord                         = pb.GetChildrenRecordResponse_ChildRecord
	GetExecuteRecordRequest                                       = pb.GetExecuteRecordRequest
	GetExecuteRecordResponse                                      = pb.GetExecuteRecordResponse
	GetInterfaceRecordRequest                                     = pb.GetInterfaceRecordRequest
	GetInterfaceRecordResponse                                    = pb.GetInterfaceRecordResponse
	GetInterfaceRecordResponse_CaseItem                           = pb.GetInterfaceRecordResponse_CaseItem
	GetParentRecordRequest                                        = pb.GetParentRecordRequest
	GetParentRecordResponse                                       = pb.GetParentRecordResponse
	GetPerfCaseRecordReq                                          = pb.GetPerfCaseRecordReq
	GetPerfCaseRecordResp                                         = pb.GetPerfCaseRecordResp
	GetPerfPlanRecordReq                                          = pb.GetPerfPlanRecordReq
	GetPerfPlanRecordResp                                         = pb.GetPerfPlanRecordResp
	GetPerfSuiteRecordReq                                         = pb.GetPerfSuiteRecordReq
	GetPerfSuiteRecordResp                                        = pb.GetPerfSuiteRecordResp
	GetPlanCasesInfoRequest                                       = pb.GetPlanCasesInfoRequest
	GetPlanCasesInfoResponse                                      = pb.GetPlanCasesInfoResponse
	GetPlanRecordRequest                                          = pb.GetPlanRecordRequest
	GetPlanRecordResponse                                         = pb.GetPlanRecordResponse
	GetPlanRecordResponse_InterfaceDocumentItem                   = pb.GetPlanRecordResponse_InterfaceDocumentItem
	GetPlanRecordResponse_ServiceItem                             = pb.GetPlanRecordResponse_ServiceItem
	GetPlanRecordResponse_SuiteItem                               = pb.GetPlanRecordResponse_SuiteItem
	GetPlanSummaryRequest                                         = pb.GetPlanSummaryRequest
	GetPlanSummaryResponse                                        = pb.GetPlanSummaryResponse
	GetPlanSummaryResponse_Record                                 = pb.GetPlanSummaryResponse_Record
	GetPlanTimeScaleRequest                                       = pb.GetPlanTimeScaleRequest
	GetPlanTimeScaleResponse                                      = pb.GetPlanTimeScaleResponse
	GetPlanTimeScaleResponse_CaseRecord                           = pb.GetPlanTimeScaleResponse_CaseRecord
	GetPlanTimeScaleResponse_SuiteRecord                          = pb.GetPlanTimeScaleResponse_SuiteRecord
	GetServiceRecordRequest                                       = pb.GetServiceRecordRequest
	GetServiceRecordResponse                                      = pb.GetServiceRecordResponse
	GetServiceRecordResponse_CaseItem                             = pb.GetServiceRecordResponse_CaseItem
	GetStabilityDeviceActivityReq                                 = pb.GetStabilityDeviceActivityReq
	GetStabilityDeviceActivityResp                                = pb.GetStabilityDeviceActivityResp
	GetStabilityDevicePerfDataReq                                 = pb.GetStabilityDevicePerfDataReq
	GetStabilityDevicePerfDataResp                                = pb.GetStabilityDevicePerfDataResp
	GetStabilityPlanRecordReq                                     = pb.GetStabilityPlanRecordReq
	GetStabilityPlanRecordResp                                    = pb.GetStabilityPlanRecordResp
	GetSuiteRecordRequest                                         = pb.GetSuiteRecordRequest
	GetSuiteRecordResponse                                        = pb.GetSuiteRecordResponse
	GetSuiteRecordResponse_CaseItem                               = pb.GetSuiteRecordResponse_CaseItem
	GetUIAgentComponentRecordReq                                  = pb.GetUIAgentComponentRecordReq
	GetUIAgentComponentRecordResp                                 = pb.GetUIAgentComponentRecordResp
	GetUICaseRecordReq                                            = pb.GetUICaseRecordReq
	GetUICaseRecordResp                                           = pb.GetUICaseRecordResp
	GetUICaseStepReq                                              = pb.GetUICaseStepReq
	GetUICaseStepResp                                             = pb.GetUICaseStepResp
	GetUIDevicePerfDataReq                                        = pb.GetUIDevicePerfDataReq
	GetUIDevicePerfDataResp                                       = pb.GetUIDevicePerfDataResp
	GetUIPlanCasesInfoRequest                                     = pb.GetUIPlanCasesInfoRequest
	GetUIPlanCasesInfoResponse                                    = pb.GetUIPlanCasesInfoResponse
	GetUIPlanRecordReq                                            = pb.GetUIPlanRecordReq
	GetUIPlanRecordResp                                           = pb.GetUIPlanRecordResp
	ListFailCaseRecordForPlanRequest                              = pb.ListFailCaseRecordForPlanRequest
	ListFailCaseRecordForPlanResponse                             = pb.ListFailCaseRecordForPlanResponse
	ListFailCaseRecordForPlanResponse_FailCaseRecordForPlanRecord = pb.ListFailCaseRecordForPlanResponse_FailCaseRecordForPlanRecord
	ListInterfaceRecordRequest                                    = pb.ListInterfaceRecordRequest
	ListInterfaceRecordResponse                                   = pb.ListInterfaceRecordResponse
	ListInterfaceRecordResponse_InterfaceRecord                   = pb.ListInterfaceRecordResponse_InterfaceRecord
	ListPlanRecordRequest                                         = pb.ListPlanRecordRequest
	ListPlanRecordResponse                                        = pb.ListPlanRecordResponse
	ListPlanRecordResponse_PlanRecord                             = pb.ListPlanRecordResponse_PlanRecord
	ListServiceRecordRequest                                      = pb.ListServiceRecordRequest
	ListServiceRecordResponse                                     = pb.ListServiceRecordResponse
	ListServiceRecordResponse_ServiceRecord                       = pb.ListServiceRecordResponse_ServiceRecord
	ListStabilityDeviceStepReq                                    = pb.ListStabilityDeviceStepReq
	ListStabilityDeviceStepResp                                   = pb.ListStabilityDeviceStepResp
	ListStabilityPlanRecordReq                                    = pb.ListStabilityPlanRecordReq
	ListStabilityPlanRecordResp                                   = pb.ListStabilityPlanRecordResp
	ListSuiteRecordRequest                                        = pb.ListSuiteRecordRequest
	ListSuiteRecordResponse                                       = pb.ListSuiteRecordResponse
	ListSuiteRecordResponse_SuiteRecord                           = pb.ListSuiteRecordResponse_SuiteRecord
	ListUICaseStepReq                                             = pb.ListUICaseStepReq
	ListUICaseStepResp                                            = pb.ListUICaseStepResp
	ListUIPlanRecordRequest                                       = pb.ListUIPlanRecordRequest
	ListUIPlanRecordResponse                                      = pb.ListUIPlanRecordResponse
	ListUIPlanRecordResponse_PlanRecord                           = pb.ListUIPlanRecordResponse_PlanRecord
	ModifyInterfaceRecordResponse                                 = pb.ModifyInterfaceRecordResponse
	ModifyPerfCaseRecordReq                                       = pb.ModifyPerfCaseRecordReq
	ModifyPerfCaseRecordResp                                      = pb.ModifyPerfCaseRecordResp
	ModifyPerfPlanRecordReq                                       = pb.ModifyPerfPlanRecordReq
	ModifyPerfPlanRecordResp                                      = pb.ModifyPerfPlanRecordResp
	ModifyPerfSuiteRecordReq                                      = pb.ModifyPerfSuiteRecordReq
	ModifyPerfSuiteRecordResp                                     = pb.ModifyPerfSuiteRecordResp
	ModifyPlanRecordResponse                                      = pb.ModifyPlanRecordResponse
	ModifyRecordResponse                                          = pb.ModifyRecordResponse
	ModifyServiceRecordResponse                                   = pb.ModifyServiceRecordResponse
	ModifyStabilityDeviceRecordResp                               = pb.ModifyStabilityDeviceRecordResp
	ModifyStabilityPlanRecordResp                                 = pb.ModifyStabilityPlanRecordResp
	ModifySuiteRecordResponse                                     = pb.ModifySuiteRecordResponse
	ModifyUIAgentComponentRecordReq                               = pb.ModifyUIAgentComponentRecordReq
	ModifyUIAgentComponentRecordResp                              = pb.ModifyUIAgentComponentRecordResp
	ModifyUICaseRecordResponse                                    = pb.ModifyUICaseRecordResponse
	ModifyUIPlanRecordResponse                                    = pb.ModifyUIPlanRecordResponse
	ModifyUISuiteRecordResponse                                   = pb.ModifyUISuiteRecordResponse
	PutInterfaceRecordRequest                                     = pb.PutInterfaceRecordRequest
	PutPlanRecordRequest                                          = pb.PutPlanRecordRequest
	PutRecordRequest                                              = pb.PutRecordRequest
	PutServiceRecordRequest                                       = pb.PutServiceRecordRequest
	PutStabilityDeviceRecordReq                                   = pb.PutStabilityDeviceRecordReq
	PutStabilityPlanRecordReq                                     = pb.PutStabilityPlanRecordReq
	PutSuiteRecordRequest                                         = pb.PutSuiteRecordRequest
	PutUICaseRecordRequest                                        = pb.PutUICaseRecordRequest
	PutUIPlanRecordRequest                                        = pb.PutUIPlanRecordRequest
	PutUISuiteRecordRequest                                       = pb.PutUISuiteRecordRequest
	RedundantPlanRecord                                           = pb.RedundantPlanRecord
	SaveUIDevicePerfDataReq                                       = pb.SaveUIDevicePerfDataReq
	SaveUIDevicePerfDataResp                                      = pb.SaveUIDevicePerfDataResp
	SearchPerfCaseRecordReq                                       = pb.SearchPerfCaseRecordReq
	SearchPerfCaseRecordResp                                      = pb.SearchPerfCaseRecordResp
	SearchPerfPlanRecordReq                                       = pb.SearchPerfPlanRecordReq
	SearchPerfPlanRecordResp                                      = pb.SearchPerfPlanRecordResp
	SearchStabilityDeviceRecordReq                                = pb.SearchStabilityDeviceRecordReq
	SearchStabilityDeviceRecordResp                               = pb.SearchStabilityDeviceRecordResp
	SearchStabilityPlanRecordReq                                  = pb.SearchStabilityPlanRecordReq
	SearchStabilityPlanRecordResp                                 = pb.SearchStabilityPlanRecordResp
	SearchUIAgentComponentRecordReq                               = pb.SearchUIAgentComponentRecordReq
	SearchUIAgentComponentRecordResp                              = pb.SearchUIAgentComponentRecordResp
	SearchUICaseRecordReq                                         = pb.SearchUICaseRecordReq
	SearchUICaseRecordResp                                        = pb.SearchUICaseRecordResp
	SearchUIDeviceRecordReq                                       = pb.SearchUIDeviceRecordReq
	SearchUIDeviceRecordResp                                      = pb.SearchUIDeviceRecordResp
	SearchUISuiteRecordReq                                        = pb.SearchUISuiteRecordReq
	SearchUISuiteRecordResp                                       = pb.SearchUISuiteRecordResp
	UpdateMonitorURLOfPerfPlanRecordReq                           = pb.UpdateMonitorURLOfPerfPlanRecordReq
	UpdateMonitorURLOfPerfPlanRecordResp                          = pb.UpdateMonitorURLOfPerfPlanRecordResp
	ViewUIPlanRecordRequest                                       = pb.ViewUIPlanRecordRequest
	ViewUIPlanRecordResponse                                      = pb.ViewUIPlanRecordResponse

	PerfReporter interface {
		// CreatePerfCaseRecord 创建压测用例执行记录
		CreatePerfCaseRecord(ctx context.Context, in *CreatePerfCaseRecordReq, opts ...grpc.CallOption) (*CreatePerfCaseRecordResp, error)
		// ModifyPerfCaseRecord 修改压测用例执行记录
		ModifyPerfCaseRecord(ctx context.Context, in *ModifyPerfCaseRecordReq, opts ...grpc.CallOption) (*ModifyPerfCaseRecordResp, error)
		// GetPerfCaseRecord 获取压测用例执行记录
		GetPerfCaseRecord(ctx context.Context, in *GetPerfCaseRecordReq, opts ...grpc.CallOption) (*GetPerfCaseRecordResp, error)
		// CreatePerfSuiteRecord 创建压测集合执行记录
		CreatePerfSuiteRecord(ctx context.Context, in *CreatePerfSuiteRecordReq, opts ...grpc.CallOption) (*CreatePerfSuiteRecordResp, error)
		// ModifyPerfSuiteRecord 修改压测集合执行记录
		ModifyPerfSuiteRecord(ctx context.Context, in *ModifyPerfSuiteRecordReq, opts ...grpc.CallOption) (*ModifyPerfSuiteRecordResp, error)
		// GetPerfSuiteRecord 获取压测集合执行记录
		GetPerfSuiteRecord(ctx context.Context, in *GetPerfSuiteRecordReq, opts ...grpc.CallOption) (*GetPerfSuiteRecordResp, error)
		// CreatePerfPlanRecord 创建压测计划执行记录
		CreatePerfPlanRecord(ctx context.Context, in *CreatePerfPlanRecordReq, opts ...grpc.CallOption) (*CreatePerfPlanRecordResp, error)
		// ModifyPerfPlanRecord 修改压测计划执行记录
		ModifyPerfPlanRecord(ctx context.Context, in *ModifyPerfPlanRecordReq, opts ...grpc.CallOption) (*ModifyPerfPlanRecordResp, error)
		// SearchPerfPlanRecord 搜索压测计划执行记录
		SearchPerfPlanRecord(ctx context.Context, in *SearchPerfPlanRecordReq, opts ...grpc.CallOption) (*SearchPerfPlanRecordResp, error)
		// GetPerfPlanRecord 获取压测计划执行记录
		GetPerfPlanRecord(ctx context.Context, in *GetPerfPlanRecordReq, opts ...grpc.CallOption) (*GetPerfPlanRecordResp, error)
		// SearchPerfCaseRecord 搜索压测计划执行记录下的压测用例执行记录
		SearchPerfCaseRecord(ctx context.Context, in *SearchPerfCaseRecordReq, opts ...grpc.CallOption) (*SearchPerfCaseRecordResp, error)
		// UpdateMonitorURLOfPerfPlanRecord 更新压测计划执行记录的`metric_url`字段
		UpdateMonitorURLOfPerfPlanRecord(ctx context.Context, in *UpdateMonitorURLOfPerfPlanRecordReq, opts ...grpc.CallOption) (*UpdateMonitorURLOfPerfPlanRecordResp, error)
	}

	defaultPerfReporter struct {
		cli zrpc.Client
	}
)

func NewPerfReporter(cli zrpc.Client) PerfReporter {
	return &defaultPerfReporter{
		cli: cli,
	}
}

// CreatePerfCaseRecord 创建压测用例执行记录
func (m *defaultPerfReporter) CreatePerfCaseRecord(ctx context.Context, in *CreatePerfCaseRecordReq, opts ...grpc.CallOption) (*CreatePerfCaseRecordResp, error) {
	client := pb.NewPerfReporterClient(m.cli.Conn())
	return client.CreatePerfCaseRecord(ctx, in, opts...)
}

// ModifyPerfCaseRecord 修改压测用例执行记录
func (m *defaultPerfReporter) ModifyPerfCaseRecord(ctx context.Context, in *ModifyPerfCaseRecordReq, opts ...grpc.CallOption) (*ModifyPerfCaseRecordResp, error) {
	client := pb.NewPerfReporterClient(m.cli.Conn())
	return client.ModifyPerfCaseRecord(ctx, in, opts...)
}

// GetPerfCaseRecord 获取压测用例执行记录
func (m *defaultPerfReporter) GetPerfCaseRecord(ctx context.Context, in *GetPerfCaseRecordReq, opts ...grpc.CallOption) (*GetPerfCaseRecordResp, error) {
	client := pb.NewPerfReporterClient(m.cli.Conn())
	return client.GetPerfCaseRecord(ctx, in, opts...)
}

// CreatePerfSuiteRecord 创建压测集合执行记录
func (m *defaultPerfReporter) CreatePerfSuiteRecord(ctx context.Context, in *CreatePerfSuiteRecordReq, opts ...grpc.CallOption) (*CreatePerfSuiteRecordResp, error) {
	client := pb.NewPerfReporterClient(m.cli.Conn())
	return client.CreatePerfSuiteRecord(ctx, in, opts...)
}

// ModifyPerfSuiteRecord 修改压测集合执行记录
func (m *defaultPerfReporter) ModifyPerfSuiteRecord(ctx context.Context, in *ModifyPerfSuiteRecordReq, opts ...grpc.CallOption) (*ModifyPerfSuiteRecordResp, error) {
	client := pb.NewPerfReporterClient(m.cli.Conn())
	return client.ModifyPerfSuiteRecord(ctx, in, opts...)
}

// GetPerfSuiteRecord 获取压测集合执行记录
func (m *defaultPerfReporter) GetPerfSuiteRecord(ctx context.Context, in *GetPerfSuiteRecordReq, opts ...grpc.CallOption) (*GetPerfSuiteRecordResp, error) {
	client := pb.NewPerfReporterClient(m.cli.Conn())
	return client.GetPerfSuiteRecord(ctx, in, opts...)
}

// CreatePerfPlanRecord 创建压测计划执行记录
func (m *defaultPerfReporter) CreatePerfPlanRecord(ctx context.Context, in *CreatePerfPlanRecordReq, opts ...grpc.CallOption) (*CreatePerfPlanRecordResp, error) {
	client := pb.NewPerfReporterClient(m.cli.Conn())
	return client.CreatePerfPlanRecord(ctx, in, opts...)
}

// ModifyPerfPlanRecord 修改压测计划执行记录
func (m *defaultPerfReporter) ModifyPerfPlanRecord(ctx context.Context, in *ModifyPerfPlanRecordReq, opts ...grpc.CallOption) (*ModifyPerfPlanRecordResp, error) {
	client := pb.NewPerfReporterClient(m.cli.Conn())
	return client.ModifyPerfPlanRecord(ctx, in, opts...)
}

// SearchPerfPlanRecord 搜索压测计划执行记录
func (m *defaultPerfReporter) SearchPerfPlanRecord(ctx context.Context, in *SearchPerfPlanRecordReq, opts ...grpc.CallOption) (*SearchPerfPlanRecordResp, error) {
	client := pb.NewPerfReporterClient(m.cli.Conn())
	return client.SearchPerfPlanRecord(ctx, in, opts...)
}

// GetPerfPlanRecord 获取压测计划执行记录
func (m *defaultPerfReporter) GetPerfPlanRecord(ctx context.Context, in *GetPerfPlanRecordReq, opts ...grpc.CallOption) (*GetPerfPlanRecordResp, error) {
	client := pb.NewPerfReporterClient(m.cli.Conn())
	return client.GetPerfPlanRecord(ctx, in, opts...)
}

// SearchPerfCaseRecord 搜索压测计划执行记录下的压测用例执行记录
func (m *defaultPerfReporter) SearchPerfCaseRecord(ctx context.Context, in *SearchPerfCaseRecordReq, opts ...grpc.CallOption) (*SearchPerfCaseRecordResp, error) {
	client := pb.NewPerfReporterClient(m.cli.Conn())
	return client.SearchPerfCaseRecord(ctx, in, opts...)
}

// UpdateMonitorURLOfPerfPlanRecord 更新压测计划执行记录的`metric_url`字段
func (m *defaultPerfReporter) UpdateMonitorURLOfPerfPlanRecord(ctx context.Context, in *UpdateMonitorURLOfPerfPlanRecordReq, opts ...grpc.CallOption) (*UpdateMonitorURLOfPerfPlanRecordResp, error) {
	client := pb.NewPerfReporterClient(m.cli.Conn())
	return client.UpdateMonitorURLOfPerfPlanRecord(ctx, in, opts...)
}

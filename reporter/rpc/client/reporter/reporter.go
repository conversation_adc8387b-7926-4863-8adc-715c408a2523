// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3
// Source: reporter.proto

package reporter

import (
	"context"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type (
	CaseFailForPlanStatForMq                                      = pb.CaseFailForPlanStatForMq
	CleanConfig                                                   = pb.CleanConfig
	CleanConfigs                                                  = pb.CleanConfigs
	CountFailedCaseInLastNDaysReq                                 = pb.CountFailedCaseInLastNDaysReq
	CountFailedCaseInLastNDaysResp                                = pb.CountFailedCaseInLastNDaysResp
	CreateInterfaceRecordResponse                                 = pb.CreateInterfaceRecordResponse
	CreatePerfCaseRecordReq                                       = pb.CreatePerfCaseRecordReq
	CreatePerfCaseRecordResp                                      = pb.CreatePerfCaseRecordResp
	CreatePerfPlanRecordReq                                       = pb.CreatePerfPlanRecordReq
	CreatePerfPlanRecordResp                                      = pb.CreatePerfPlanRecordResp
	CreatePerfSuiteRecordReq                                      = pb.CreatePerfSuiteRecordReq
	CreatePerfSuiteRecordResp                                     = pb.CreatePerfSuiteRecordResp
	CreatePlanRecordResponse                                      = pb.CreatePlanRecordResponse
	CreateRecordResponse                                          = pb.CreateRecordResponse
	CreateServiceRecordResponse                                   = pb.CreateServiceRecordResponse
	CreateStabilityDeviceRecordResp                               = pb.CreateStabilityDeviceRecordResp
	CreateStabilityPlanRecordResp                                 = pb.CreateStabilityPlanRecordResp
	CreateSuiteRecordResponse                                     = pb.CreateSuiteRecordResponse
	CreateUIAgentComponentRecordReq                               = pb.CreateUIAgentComponentRecordReq
	CreateUIAgentComponentRecordResp                              = pb.CreateUIAgentComponentRecordResp
	CreateUICaseRecordResponse                                    = pb.CreateUICaseRecordResponse
	CreateUIPlanRecordResponse                                    = pb.CreateUIPlanRecordResponse
	CreateUISuiteRecordResponse                                   = pb.CreateUISuiteRecordResponse
	DelCaseFailStatForPlanReq                                     = pb.DelCaseFailStatForPlanReq
	DelCaseFailStatForPlanResp                                    = pb.DelCaseFailStatForPlanResp
	FindRedundantPlanRecordResp                                   = pb.FindRedundantPlanRecordResp
	GetCaseLatestRecordRequest                                    = pb.GetCaseLatestRecordRequest
	GetCaseLatestRecordResponse                                   = pb.GetCaseLatestRecordResponse
	GetCaseLatestRecordResponse_RecordCaseRecord                  = pb.GetCaseLatestRecordResponse_RecordCaseRecord
	GetChildrenRecordRequest                                      = pb.GetChildrenRecordRequest
	GetChildrenRecordResponse                                     = pb.GetChildrenRecordResponse
	GetChildrenRecordResponse_ChildRecord                         = pb.GetChildrenRecordResponse_ChildRecord
	GetExecuteRecordRequest                                       = pb.GetExecuteRecordRequest
	GetExecuteRecordResponse                                      = pb.GetExecuteRecordResponse
	GetInterfaceRecordRequest                                     = pb.GetInterfaceRecordRequest
	GetInterfaceRecordResponse                                    = pb.GetInterfaceRecordResponse
	GetInterfaceRecordResponse_CaseItem                           = pb.GetInterfaceRecordResponse_CaseItem
	GetParentRecordRequest                                        = pb.GetParentRecordRequest
	GetParentRecordResponse                                       = pb.GetParentRecordResponse
	GetPerfCaseRecordReq                                          = pb.GetPerfCaseRecordReq
	GetPerfCaseRecordResp                                         = pb.GetPerfCaseRecordResp
	GetPerfPlanRecordReq                                          = pb.GetPerfPlanRecordReq
	GetPerfPlanRecordResp                                         = pb.GetPerfPlanRecordResp
	GetPerfSuiteRecordReq                                         = pb.GetPerfSuiteRecordReq
	GetPerfSuiteRecordResp                                        = pb.GetPerfSuiteRecordResp
	GetPlanCasesInfoRequest                                       = pb.GetPlanCasesInfoRequest
	GetPlanCasesInfoResponse                                      = pb.GetPlanCasesInfoResponse
	GetPlanRecordRequest                                          = pb.GetPlanRecordRequest
	GetPlanRecordResponse                                         = pb.GetPlanRecordResponse
	GetPlanRecordResponse_InterfaceDocumentItem                   = pb.GetPlanRecordResponse_InterfaceDocumentItem
	GetPlanRecordResponse_ServiceItem                             = pb.GetPlanRecordResponse_ServiceItem
	GetPlanRecordResponse_SuiteItem                               = pb.GetPlanRecordResponse_SuiteItem
	GetPlanSummaryRequest                                         = pb.GetPlanSummaryRequest
	GetPlanSummaryResponse                                        = pb.GetPlanSummaryResponse
	GetPlanSummaryResponse_Record                                 = pb.GetPlanSummaryResponse_Record
	GetPlanTimeScaleRequest                                       = pb.GetPlanTimeScaleRequest
	GetPlanTimeScaleResponse                                      = pb.GetPlanTimeScaleResponse
	GetPlanTimeScaleResponse_CaseRecord                           = pb.GetPlanTimeScaleResponse_CaseRecord
	GetPlanTimeScaleResponse_SuiteRecord                          = pb.GetPlanTimeScaleResponse_SuiteRecord
	GetServiceRecordRequest                                       = pb.GetServiceRecordRequest
	GetServiceRecordResponse                                      = pb.GetServiceRecordResponse
	GetServiceRecordResponse_CaseItem                             = pb.GetServiceRecordResponse_CaseItem
	GetStabilityDeviceActivityReq                                 = pb.GetStabilityDeviceActivityReq
	GetStabilityDeviceActivityResp                                = pb.GetStabilityDeviceActivityResp
	GetStabilityDevicePerfDataReq                                 = pb.GetStabilityDevicePerfDataReq
	GetStabilityDevicePerfDataResp                                = pb.GetStabilityDevicePerfDataResp
	GetStabilityPlanRecordReq                                     = pb.GetStabilityPlanRecordReq
	GetStabilityPlanRecordResp                                    = pb.GetStabilityPlanRecordResp
	GetSuiteRecordRequest                                         = pb.GetSuiteRecordRequest
	GetSuiteRecordResponse                                        = pb.GetSuiteRecordResponse
	GetSuiteRecordResponse_CaseItem                               = pb.GetSuiteRecordResponse_CaseItem
	GetUIAgentComponentRecordReq                                  = pb.GetUIAgentComponentRecordReq
	GetUIAgentComponentRecordResp                                 = pb.GetUIAgentComponentRecordResp
	GetUICaseRecordReq                                            = pb.GetUICaseRecordReq
	GetUICaseRecordResp                                           = pb.GetUICaseRecordResp
	GetUICaseStepReq                                              = pb.GetUICaseStepReq
	GetUICaseStepResp                                             = pb.GetUICaseStepResp
	GetUIDevicePerfDataReq                                        = pb.GetUIDevicePerfDataReq
	GetUIDevicePerfDataResp                                       = pb.GetUIDevicePerfDataResp
	GetUIPlanCasesInfoRequest                                     = pb.GetUIPlanCasesInfoRequest
	GetUIPlanCasesInfoResponse                                    = pb.GetUIPlanCasesInfoResponse
	GetUIPlanRecordReq                                            = pb.GetUIPlanRecordReq
	GetUIPlanRecordResp                                           = pb.GetUIPlanRecordResp
	ListFailCaseRecordForPlanRequest                              = pb.ListFailCaseRecordForPlanRequest
	ListFailCaseRecordForPlanResponse                             = pb.ListFailCaseRecordForPlanResponse
	ListFailCaseRecordForPlanResponse_FailCaseRecordForPlanRecord = pb.ListFailCaseRecordForPlanResponse_FailCaseRecordForPlanRecord
	ListInterfaceRecordRequest                                    = pb.ListInterfaceRecordRequest
	ListInterfaceRecordResponse                                   = pb.ListInterfaceRecordResponse
	ListInterfaceRecordResponse_InterfaceRecord                   = pb.ListInterfaceRecordResponse_InterfaceRecord
	ListPlanRecordRequest                                         = pb.ListPlanRecordRequest
	ListPlanRecordResponse                                        = pb.ListPlanRecordResponse
	ListPlanRecordResponse_PlanRecord                             = pb.ListPlanRecordResponse_PlanRecord
	ListServiceRecordRequest                                      = pb.ListServiceRecordRequest
	ListServiceRecordResponse                                     = pb.ListServiceRecordResponse
	ListServiceRecordResponse_ServiceRecord                       = pb.ListServiceRecordResponse_ServiceRecord
	ListStabilityDeviceStepReq                                    = pb.ListStabilityDeviceStepReq
	ListStabilityDeviceStepResp                                   = pb.ListStabilityDeviceStepResp
	ListStabilityPlanRecordReq                                    = pb.ListStabilityPlanRecordReq
	ListStabilityPlanRecordResp                                   = pb.ListStabilityPlanRecordResp
	ListSuiteRecordRequest                                        = pb.ListSuiteRecordRequest
	ListSuiteRecordResponse                                       = pb.ListSuiteRecordResponse
	ListSuiteRecordResponse_SuiteRecord                           = pb.ListSuiteRecordResponse_SuiteRecord
	ListUICaseStepReq                                             = pb.ListUICaseStepReq
	ListUICaseStepResp                                            = pb.ListUICaseStepResp
	ListUIPlanRecordRequest                                       = pb.ListUIPlanRecordRequest
	ListUIPlanRecordResponse                                      = pb.ListUIPlanRecordResponse
	ListUIPlanRecordResponse_PlanRecord                           = pb.ListUIPlanRecordResponse_PlanRecord
	ModifyInterfaceRecordResponse                                 = pb.ModifyInterfaceRecordResponse
	ModifyPerfCaseRecordReq                                       = pb.ModifyPerfCaseRecordReq
	ModifyPerfCaseRecordResp                                      = pb.ModifyPerfCaseRecordResp
	ModifyPerfPlanRecordReq                                       = pb.ModifyPerfPlanRecordReq
	ModifyPerfPlanRecordResp                                      = pb.ModifyPerfPlanRecordResp
	ModifyPerfSuiteRecordReq                                      = pb.ModifyPerfSuiteRecordReq
	ModifyPerfSuiteRecordResp                                     = pb.ModifyPerfSuiteRecordResp
	ModifyPlanRecordResponse                                      = pb.ModifyPlanRecordResponse
	ModifyRecordResponse                                          = pb.ModifyRecordResponse
	ModifyServiceRecordResponse                                   = pb.ModifyServiceRecordResponse
	ModifyStabilityDeviceRecordResp                               = pb.ModifyStabilityDeviceRecordResp
	ModifyStabilityPlanRecordResp                                 = pb.ModifyStabilityPlanRecordResp
	ModifySuiteRecordResponse                                     = pb.ModifySuiteRecordResponse
	ModifyUIAgentComponentRecordReq                               = pb.ModifyUIAgentComponentRecordReq
	ModifyUIAgentComponentRecordResp                              = pb.ModifyUIAgentComponentRecordResp
	ModifyUICaseRecordResponse                                    = pb.ModifyUICaseRecordResponse
	ModifyUIPlanRecordResponse                                    = pb.ModifyUIPlanRecordResponse
	ModifyUISuiteRecordResponse                                   = pb.ModifyUISuiteRecordResponse
	PutInterfaceRecordRequest                                     = pb.PutInterfaceRecordRequest
	PutPlanRecordRequest                                          = pb.PutPlanRecordRequest
	PutRecordRequest                                              = pb.PutRecordRequest
	PutServiceRecordRequest                                       = pb.PutServiceRecordRequest
	PutStabilityDeviceRecordReq                                   = pb.PutStabilityDeviceRecordReq
	PutStabilityPlanRecordReq                                     = pb.PutStabilityPlanRecordReq
	PutSuiteRecordRequest                                         = pb.PutSuiteRecordRequest
	PutUICaseRecordRequest                                        = pb.PutUICaseRecordRequest
	PutUIPlanRecordRequest                                        = pb.PutUIPlanRecordRequest
	PutUISuiteRecordRequest                                       = pb.PutUISuiteRecordRequest
	RedundantPlanRecord                                           = pb.RedundantPlanRecord
	SaveUIDevicePerfDataReq                                       = pb.SaveUIDevicePerfDataReq
	SaveUIDevicePerfDataResp                                      = pb.SaveUIDevicePerfDataResp
	SearchPerfCaseRecordReq                                       = pb.SearchPerfCaseRecordReq
	SearchPerfCaseRecordResp                                      = pb.SearchPerfCaseRecordResp
	SearchPerfPlanRecordReq                                       = pb.SearchPerfPlanRecordReq
	SearchPerfPlanRecordResp                                      = pb.SearchPerfPlanRecordResp
	SearchStabilityDeviceRecordReq                                = pb.SearchStabilityDeviceRecordReq
	SearchStabilityDeviceRecordResp                               = pb.SearchStabilityDeviceRecordResp
	SearchStabilityPlanRecordReq                                  = pb.SearchStabilityPlanRecordReq
	SearchStabilityPlanRecordResp                                 = pb.SearchStabilityPlanRecordResp
	SearchUIAgentComponentRecordReq                               = pb.SearchUIAgentComponentRecordReq
	SearchUIAgentComponentRecordResp                              = pb.SearchUIAgentComponentRecordResp
	SearchUICaseRecordReq                                         = pb.SearchUICaseRecordReq
	SearchUICaseRecordResp                                        = pb.SearchUICaseRecordResp
	SearchUIDeviceRecordReq                                       = pb.SearchUIDeviceRecordReq
	SearchUIDeviceRecordResp                                      = pb.SearchUIDeviceRecordResp
	SearchUISuiteRecordReq                                        = pb.SearchUISuiteRecordReq
	SearchUISuiteRecordResp                                       = pb.SearchUISuiteRecordResp
	UpdateMonitorURLOfPerfPlanRecordReq                           = pb.UpdateMonitorURLOfPerfPlanRecordReq
	UpdateMonitorURLOfPerfPlanRecordResp                          = pb.UpdateMonitorURLOfPerfPlanRecordResp
	ViewUIPlanRecordRequest                                       = pb.ViewUIPlanRecordRequest
	ViewUIPlanRecordResponse                                      = pb.ViewUIPlanRecordResponse

	Reporter interface {
		// 组件组及用例记录 RPC接口
		CreateRecord(ctx context.Context, in *PutRecordRequest, opts ...grpc.CallOption) (*CreateRecordResponse, error)
		ModifyRecord(ctx context.Context, in *PutRecordRequest, opts ...grpc.CallOption) (*ModifyRecordResponse, error)
		// 查询自身的RPC接口
		GetExecuteRecord(ctx context.Context, in *GetExecuteRecordRequest, opts ...grpc.CallOption) (*GetExecuteRecordResponse, error)
		// 查询Parent的RPC接口
		GetParentRecord(ctx context.Context, in *GetParentRecordRequest, opts ...grpc.CallOption) (*GetParentRecordResponse, error)
		// 查询Children的RPC接口
		GetChildrenRecord(ctx context.Context, in *GetChildrenRecordRequest, opts ...grpc.CallOption) (*GetChildrenRecordResponse, error)
		// `接口`
		CreateInterfaceRecord(ctx context.Context, in *PutInterfaceRecordRequest, opts ...grpc.CallOption) (*CreateInterfaceRecordResponse, error)
		// 修改接口记录
		ModifyInterfaceRecord(ctx context.Context, in *PutInterfaceRecordRequest, opts ...grpc.CallOption) (*ModifyInterfaceRecordResponse, error)
		// 获取接口中用例最新一次执行记录
		GetCaseLatestRecord(ctx context.Context, in *GetCaseLatestRecordRequest, opts ...grpc.CallOption) (*GetCaseLatestRecordResponse, error)
		// 接口`调试`记录列表
		ListInterfaceRecord(ctx context.Context, in *ListInterfaceRecordRequest, opts ...grpc.CallOption) (*ListInterfaceRecordResponse, error)
		// 接口执行记录详情
		GetInterfaceRecord(ctx context.Context, in *GetInterfaceRecordRequest, opts ...grpc.CallOption) (*GetInterfaceRecordResponse, error)
		// `集合`
		CreateSuiteRecord(ctx context.Context, in *PutSuiteRecordRequest, opts ...grpc.CallOption) (*CreateSuiteRecordResponse, error)
		// 修改集合记录
		ModifySuiteRecord(ctx context.Context, in *PutSuiteRecordRequest, opts ...grpc.CallOption) (*ModifySuiteRecordResponse, error)
		// 集合`调试`记录列表
		ListSuiteRecord(ctx context.Context, in *ListSuiteRecordRequest, opts ...grpc.CallOption) (*ListSuiteRecordResponse, error)
		// 集合执行记录详情
		GetSuiteRecord(ctx context.Context, in *GetSuiteRecordRequest, opts ...grpc.CallOption) (*GetSuiteRecordResponse, error)
		// `精准测试服务`
		CreateServiceRecord(ctx context.Context, in *PutServiceRecordRequest, opts ...grpc.CallOption) (*CreateServiceRecordResponse, error)
		// 修改精准测试服务记录
		ModifyServiceRecord(ctx context.Context, in *PutServiceRecordRequest, opts ...grpc.CallOption) (*ModifyServiceRecordResponse, error)
		// 精准测试服务`调试`记录列表
		ListServiceRecord(ctx context.Context, in *ListServiceRecordRequest, opts ...grpc.CallOption) (*ListServiceRecordResponse, error)
		// 精准测试服务执行记录详情
		GetServiceRecord(ctx context.Context, in *GetServiceRecordRequest, opts ...grpc.CallOption) (*GetServiceRecordResponse, error)
		// `计划`
		CreatePlanRecord(ctx context.Context, in *PutPlanRecordRequest, opts ...grpc.CallOption) (*CreatePlanRecordResponse, error)
		// 修改计划记录
		ModifyPlanRecord(ctx context.Context, in *PutPlanRecordRequest, opts ...grpc.CallOption) (*ModifyPlanRecordResponse, error)
		// 计划执行记录列表
		ListPlanRecord(ctx context.Context, in *ListPlanRecordRequest, opts ...grpc.CallOption) (*ListPlanRecordResponse, error)
		// 计划执行记录详情
		GetPlanRecord(ctx context.Context, in *GetPlanRecordRequest, opts ...grpc.CallOption) (*GetPlanRecordResponse, error)
		// 计划执行详情查看其下各集合用例执行时间刻度信息
		GetPlanTimeScale(ctx context.Context, in *GetPlanTimeScaleRequest, opts ...grpc.CallOption) (*GetPlanTimeScaleResponse, error)
		// 获取测试计划执行报告（ci/cd专用）
		GetPlanSummary(ctx context.Context, in *GetPlanSummaryRequest, opts ...grpc.CallOption) (*GetPlanSummaryResponse, error)
		// 获取API计划关联用例信息
		GetPlanCasesInfo(ctx context.Context, in *GetPlanCasesInfoRequest, opts ...grpc.CallOption) (*GetPlanCasesInfoResponse, error)
		// `用例`
		ListFailCaseForPlanRecord(ctx context.Context, in *ListFailCaseRecordForPlanRequest, opts ...grpc.CallOption) (*ListFailCaseRecordForPlanResponse, error)
		DelCaseFailStatForPlan(ctx context.Context, in *DelCaseFailStatForPlanReq, opts ...grpc.CallOption) (*DelCaseFailStatForPlanResp, error)
		// CountFailedCaseInLastNDays 统计最近N天指定用例的失败数
		CountFailedCaseInLastNDays(ctx context.Context, in *CountFailedCaseInLastNDaysReq, opts ...grpc.CallOption) (*CountFailedCaseInLastNDaysResp, error)
	}

	defaultReporter struct {
		cli zrpc.Client
	}
)

func NewReporter(cli zrpc.Client) Reporter {
	return &defaultReporter{
		cli: cli,
	}
}

// 组件组及用例记录 RPC接口
func (m *defaultReporter) CreateRecord(ctx context.Context, in *PutRecordRequest, opts ...grpc.CallOption) (*CreateRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.CreateRecord(ctx, in, opts...)
}

func (m *defaultReporter) ModifyRecord(ctx context.Context, in *PutRecordRequest, opts ...grpc.CallOption) (*ModifyRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.ModifyRecord(ctx, in, opts...)
}

// 查询自身的RPC接口
func (m *defaultReporter) GetExecuteRecord(ctx context.Context, in *GetExecuteRecordRequest, opts ...grpc.CallOption) (*GetExecuteRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.GetExecuteRecord(ctx, in, opts...)
}

// 查询Parent的RPC接口
func (m *defaultReporter) GetParentRecord(ctx context.Context, in *GetParentRecordRequest, opts ...grpc.CallOption) (*GetParentRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.GetParentRecord(ctx, in, opts...)
}

// 查询Children的RPC接口
func (m *defaultReporter) GetChildrenRecord(ctx context.Context, in *GetChildrenRecordRequest, opts ...grpc.CallOption) (*GetChildrenRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.GetChildrenRecord(ctx, in, opts...)
}

// `接口`
func (m *defaultReporter) CreateInterfaceRecord(ctx context.Context, in *PutInterfaceRecordRequest, opts ...grpc.CallOption) (*CreateInterfaceRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.CreateInterfaceRecord(ctx, in, opts...)
}

// 修改接口记录
func (m *defaultReporter) ModifyInterfaceRecord(ctx context.Context, in *PutInterfaceRecordRequest, opts ...grpc.CallOption) (*ModifyInterfaceRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.ModifyInterfaceRecord(ctx, in, opts...)
}

// 获取接口中用例最新一次执行记录
func (m *defaultReporter) GetCaseLatestRecord(ctx context.Context, in *GetCaseLatestRecordRequest, opts ...grpc.CallOption) (*GetCaseLatestRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.GetCaseLatestRecord(ctx, in, opts...)
}

// 接口`调试`记录列表
func (m *defaultReporter) ListInterfaceRecord(ctx context.Context, in *ListInterfaceRecordRequest, opts ...grpc.CallOption) (*ListInterfaceRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.ListInterfaceRecord(ctx, in, opts...)
}

// 接口执行记录详情
func (m *defaultReporter) GetInterfaceRecord(ctx context.Context, in *GetInterfaceRecordRequest, opts ...grpc.CallOption) (*GetInterfaceRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.GetInterfaceRecord(ctx, in, opts...)
}

// `集合`
func (m *defaultReporter) CreateSuiteRecord(ctx context.Context, in *PutSuiteRecordRequest, opts ...grpc.CallOption) (*CreateSuiteRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.CreateSuiteRecord(ctx, in, opts...)
}

// 修改集合记录
func (m *defaultReporter) ModifySuiteRecord(ctx context.Context, in *PutSuiteRecordRequest, opts ...grpc.CallOption) (*ModifySuiteRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.ModifySuiteRecord(ctx, in, opts...)
}

// 集合`调试`记录列表
func (m *defaultReporter) ListSuiteRecord(ctx context.Context, in *ListSuiteRecordRequest, opts ...grpc.CallOption) (*ListSuiteRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.ListSuiteRecord(ctx, in, opts...)
}

// 集合执行记录详情
func (m *defaultReporter) GetSuiteRecord(ctx context.Context, in *GetSuiteRecordRequest, opts ...grpc.CallOption) (*GetSuiteRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.GetSuiteRecord(ctx, in, opts...)
}

// `精准测试服务`
func (m *defaultReporter) CreateServiceRecord(ctx context.Context, in *PutServiceRecordRequest, opts ...grpc.CallOption) (*CreateServiceRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.CreateServiceRecord(ctx, in, opts...)
}

// 修改精准测试服务记录
func (m *defaultReporter) ModifyServiceRecord(ctx context.Context, in *PutServiceRecordRequest, opts ...grpc.CallOption) (*ModifyServiceRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.ModifyServiceRecord(ctx, in, opts...)
}

// 精准测试服务`调试`记录列表
func (m *defaultReporter) ListServiceRecord(ctx context.Context, in *ListServiceRecordRequest, opts ...grpc.CallOption) (*ListServiceRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.ListServiceRecord(ctx, in, opts...)
}

// 精准测试服务执行记录详情
func (m *defaultReporter) GetServiceRecord(ctx context.Context, in *GetServiceRecordRequest, opts ...grpc.CallOption) (*GetServiceRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.GetServiceRecord(ctx, in, opts...)
}

// `计划`
func (m *defaultReporter) CreatePlanRecord(ctx context.Context, in *PutPlanRecordRequest, opts ...grpc.CallOption) (*CreatePlanRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.CreatePlanRecord(ctx, in, opts...)
}

// 修改计划记录
func (m *defaultReporter) ModifyPlanRecord(ctx context.Context, in *PutPlanRecordRequest, opts ...grpc.CallOption) (*ModifyPlanRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.ModifyPlanRecord(ctx, in, opts...)
}

// 计划执行记录列表
func (m *defaultReporter) ListPlanRecord(ctx context.Context, in *ListPlanRecordRequest, opts ...grpc.CallOption) (*ListPlanRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.ListPlanRecord(ctx, in, opts...)
}

// 计划执行记录详情
func (m *defaultReporter) GetPlanRecord(ctx context.Context, in *GetPlanRecordRequest, opts ...grpc.CallOption) (*GetPlanRecordResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.GetPlanRecord(ctx, in, opts...)
}

// 计划执行详情查看其下各集合用例执行时间刻度信息
func (m *defaultReporter) GetPlanTimeScale(ctx context.Context, in *GetPlanTimeScaleRequest, opts ...grpc.CallOption) (*GetPlanTimeScaleResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.GetPlanTimeScale(ctx, in, opts...)
}

// 获取测试计划执行报告（ci/cd专用）
func (m *defaultReporter) GetPlanSummary(ctx context.Context, in *GetPlanSummaryRequest, opts ...grpc.CallOption) (*GetPlanSummaryResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.GetPlanSummary(ctx, in, opts...)
}

// 获取API计划关联用例信息
func (m *defaultReporter) GetPlanCasesInfo(ctx context.Context, in *GetPlanCasesInfoRequest, opts ...grpc.CallOption) (*GetPlanCasesInfoResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.GetPlanCasesInfo(ctx, in, opts...)
}

// `用例`
func (m *defaultReporter) ListFailCaseForPlanRecord(ctx context.Context, in *ListFailCaseRecordForPlanRequest, opts ...grpc.CallOption) (*ListFailCaseRecordForPlanResponse, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.ListFailCaseForPlanRecord(ctx, in, opts...)
}

func (m *defaultReporter) DelCaseFailStatForPlan(ctx context.Context, in *DelCaseFailStatForPlanReq, opts ...grpc.CallOption) (*DelCaseFailStatForPlanResp, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.DelCaseFailStatForPlan(ctx, in, opts...)
}

// CountFailedCaseInLastNDays 统计最近N天指定用例的失败数
func (m *defaultReporter) CountFailedCaseInLastNDays(ctx context.Context, in *CountFailedCaseInLastNDaysReq, opts ...grpc.CallOption) (*CountFailedCaseInLastNDaysResp, error) {
	client := pb.NewReporterClient(m.cli.Conn())
	return client.CountFailedCaseInLastNDays(ctx, in, opts...)
}

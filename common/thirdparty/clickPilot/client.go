package clickPilot

import (
	"net/http"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/fasthttp"
)

// Client ClickPilot客户端
type Client struct {
	c    *fasthttp.Client
	conf Config
}

// NewClient 创建ClickPilot客户端
func NewClient(conf Config) *Client {
	return &Client{
		c: fasthttp.NewClient(
			fasthttp.ClientConf{
				BaseURL: conf.BaseURL,
			},
		),
		conf: conf,
	}
}

// CreateUITask 创建UI任务
func (c *Client) CreateUITask(in *CreateUITaskReq) (*CreateUITaskResp, error) {
	apiName := createUITaskAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodPost),
		fasthttp.SetHeader(http.Header{"Content-Type": {"application/json"}}),
		fasthttp.SetBody(jsonx.MarshalIgnoreError(in)),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	if err := c.c.Send(req, resp, requestTimeout); err != nil {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of ClickPilot, api: %s, error: %+v",
			apiName, err,
		)
	}

	body := resp.Body()
	status := resp.StatusCode()
	if status != http.StatusOK {
		return nil, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of ClickPilot, api: %s, status code: %d, resp body: %s",
			apiName, status, body,
		)
	}

	var out CreateUITaskResp
	if err := jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	return &out, nil
}

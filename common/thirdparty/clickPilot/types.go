package clickPilot

// Config ClickPilot客户端配置
type Config struct {
	BaseURL string `json:",default=http://localhost:8080"` // ClickPilot服务地址
}

// AgentType 代理类型
type AgentType string

const (
	AgentTypeAndroid AgentType = "android"
	AgentTypeIOS     AgentType = "ios"
)

// DeviceType 设备类型
type DeviceType string

const (
	DeviceTypeAndroid DeviceType = "android"
	DeviceTypeIOS     DeviceType = "ios"
)

// CreateUITaskReq 创建UI任务请求
type CreateUITaskReq struct {
	TaskName                string            `json:"task_name"`                           // 测试用例名称
	TaskExpectResult        *TaskExpectResult `json:"task_expect_result"`                  // 整个用例期望结果
	TaskStepByStep          []*TaskStep       `json:"task_step_by_step"`                   // 分步骤验证模式（可选）
	TaskAggregationStep     string            `json:"task_aggregation_step"`               // 分步骤聚合模式（可选）
	AgentType               AgentType         `json:"agent_type"`                          // android / ios，默认android
	AgentConfigID           string            `json:"agent_config_id"`                     // agent的开放Prompt，默认值'tt'
	Device                  *Device           `json:"device"`                              // 设备配置
	AppID                   string            `json:"app_id"`                              // 执行的软件app_id
	AppName                 string            `json:"app_name,omitempty"`                  // 应用名称
	AppDescription          string            `json:"app_description,omitempty"`           // 软件功能的介绍
	UIComponentInstructions string            `json:"ui_component_instructions,omitempty"` // UI组件操作说明
	SpecialScenarios        string            `json:"special_scenarios,omitempty"`         // 特殊场景处理说明
	Restart                 string            `json:"restart,omitempty"`                   // 是否重启app
}

// TaskExpectResult 任务期望结果
type TaskExpectResult struct {
	Text  string `json:"text,omitempty"`  // 期望结果文字描述
	Image string `json:"image,omitempty"` // 期望结果图片路径（可选）
}

// TaskStep 任务步骤
type TaskStep struct {
	Step         string            `json:"step"`          // 步骤名称
	ExpectResult *TaskExpectResult `json:"expect_result"` // 每一步期望结果（可选）
}

// Device 设备配置
type Device struct {
	Type    DeviceType     `json:"type"`              // android / ios
	Android *AndroidDevice `json:"android,omitempty"` // Android设备配置
	IOS     *IOSDevice     `json:"ios,omitempty"`     // iOS设备配置
}

// AndroidDevice Android设备配置
type AndroidDevice struct {
	URL string `json:"url"` // adb连接地址
}

// IOSDevice iOS设备配置
type IOSDevice struct {
	URL string `json:"url"` // iOS设备连接地址
}

// CreateUITaskResp 创建UI任务响应
type CreateUITaskResp struct {
	Code    int                   `json:"code"`
	Message string                `json:"message"`
	Data    *CreateUITaskRespData `json:"data"`
}

// CreateUITaskRespData 创建UI任务响应数据
type CreateUITaskRespData struct {
	TaskID string `json:"task_id"`
}

package clickPilot

import (
	"fmt"
	"log"
)

// ExampleClient_CreateUITask 展示如何使用ClickPilot客户端创建UI任务
func ExampleClient_CreateUITask() {
	// 创建客户端配置
	conf := Config{
		BaseURL: "http://localhost:8080", // 替换为实际的ClickPilot服务地址
	}
	
	// 创建客户端
	client := NewClient(conf)
	
	// 构建创建UI任务请求
	req := &CreateUITaskReq{
		TaskName: "修改生日",
		TaskExpectResult: &TaskExpectResult{
			Text:  "生日修改成功",
			Image: "image_path",
		},
		TaskStepByStep: []*TaskStep{
			{
				Step: "点击我tab",
				ExpectResult: &TaskExpectResult{
					Text:  "进入个人中心",
					Image: "image_path",
				},
			},
			{
				Step: "点击编辑资料",
				ExpectResult: &TaskExpectResult{
					Text:  "进入资料编辑页面",
					Image: "image_path",
				},
			},
		},
		TaskAggregationStep: "1.点击我tab 2.点击编辑资料 3.修改生日",
		AgentType:           AgentTypeAndroid,
		AgentConfigID:       "tt",
		Device: &Device{
			Type: DeviceTypeAndroid,
			Android: &AndroidDevice{
				URL: "127.0.0.1:5555",
			},
		},
		AppID:                   "tt_voice",
		AppName:                 "TT语音",
		AppDescription:          "TT语音是一款专为游戏玩家打造的语音社交软件...",
		UIComponentInstructions: "1.***目标选择器*** -- 组件结构...",
		SpecialScenarios:        "***遇到以下情况时必须流程规避处理: ** -- 弹窗页面出现弹窗...",
		Restart:                 "",
	}
	
	// 调用创建UI任务接口
	resp, err := client.CreateUITask(req)
	if err != nil {
		log.Printf("创建UI任务失败: %v", err)
		return
	}
	
	// 处理响应
	if resp.Code == 0 {
		fmt.Printf("创建UI任务成功，任务ID: %s\n", resp.Data.TaskID)
	} else {
		fmt.Printf("创建UI任务失败，错误信息: %s\n", resp.Message)
	}
}

// ExampleClient_CreateUITask_iOS 展示如何为iOS设备创建UI任务
func ExampleClient_CreateUITask_iOS() {
	conf := Config{
		BaseURL: "http://localhost:8080",
	}
	
	client := NewClient(conf)
	
	req := &CreateUITaskReq{
		TaskName: "iOS设备测试",
		TaskExpectResult: &TaskExpectResult{
			Text: "测试完成",
		},
		AgentType:     AgentTypeIOS,
		AgentConfigID: "tt",
		Device: &Device{
			Type: DeviceTypeIOS,
			IOS: &IOSDevice{
				URL: "ios-device-url",
			},
		},
		AppID: "com.example.app",
	}
	
	resp, err := client.CreateUITask(req)
	if err != nil {
		log.Printf("创建iOS UI任务失败: %v", err)
		return
	}
	
	fmt.Printf("iOS任务创建结果: %+v\n", resp)
}

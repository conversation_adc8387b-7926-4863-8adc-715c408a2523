# ClickPilot 客户端

ClickPilot 客户端用于与 ClickPilot UI 自动化测试服务进行交互。

## 功能特性

- 支持创建 UI 自动化测试任务
- 支持 Android 和 iOS 设备
- 使用 fasthttp 客户端进行高性能 HTTP 通信
- 完整的错误处理和响应解析

## 使用方法

### 1. 创建客户端

```go
import "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/clickPilot"

// 配置客户端
conf := clickPilot.Config{
    BaseURL: "http://your-clickpilot-server:8080",
}

// 创建客户端实例
client := clickPilot.NewClient(conf)
```

### 2. 创建 UI 任务

```go
// 构建请求
req := &clickPilot.CreateUITaskReq{
    TaskName: "修改生日",
    TaskExpectResult: &clickPilot.TaskExpectResult{
        Text:  "生日修改成功",
        Image: "image_path",
    },
    TaskStepByStep: []*clickPilot.TaskStep{
        {
            Step: "点击我tab",
            ExpectResult: &clickPilot.TaskExpectResult{
                Text:  "进入个人中心",
                Image: "image_path",
            },
        },
    },
    AgentType:     clickPilot.AgentTypeAndroid,
    AgentConfigID: "tt",
    Device: &clickPilot.Device{
        Type: clickPilot.DeviceTypeAndroid,
        Android: &clickPilot.AndroidDevice{
            URL: "127.0.0.1:5555",
        },
    },
    AppID: "tt_voice",
}

// 发送请求
resp, err := client.CreateUITask(req)
if err != nil {
    log.Printf("创建任务失败: %v", err)
    return
}

// 处理响应
if resp.Code == 0 {
    fmt.Printf("任务创建成功，ID: %s\n", resp.Data.TaskID)
} else {
    fmt.Printf("任务创建失败: %s\n", resp.Message)
}
```

## 数据结构

### 代理类型 (AgentType)

- `AgentTypeAndroid`: Android 设备代理
- `AgentTypeIOS`: iOS 设备代理

### 设备类型 (DeviceType)

- `DeviceTypeAndroid`: Android 设备
- `DeviceTypeIOS`: iOS 设备

### 主要结构体

- `CreateUITaskReq`: 创建 UI 任务请求
- `CreateUITaskResp`: 创建 UI 任务响应
- `TaskExpectResult`: 任务期望结果
- `TaskStep`: 任务步骤
- `Device`: 设备配置
- `AndroidDevice`: Android 设备配置
- `IOSDevice`: iOS 设备配置

## API 接口

### POST /api/v1/ui-task/create

创建 UI 自动化测试任务。

**请求参数**: `CreateUITaskReq`
**响应数据**: `CreateUITaskResp`

## 错误处理

客户端使用 `gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx` 进行统一的错误处理：

- `CallExternalAPIFailure`: 外部 API 调用失败
- `SerializationError`: 序列化/反序列化错误

## 测试

运行测试：

```bash
go test -v ./...
```

查看使用示例：

```bash
go test -v -run Example
```

package clickPilot

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewClient(t *testing.T) {
	conf := Config{
		BaseURL: "http://localhost:8080",
	}
	
	client := NewClient(conf)
	assert.NotNil(t, client)
	assert.Equal(t, conf.BaseURL, client.conf.BaseURL)
}

func TestCreateUITaskReq_Structure(t *testing.T) {
	// 测试请求结构体是否正确定义
	req := &CreateUITaskReq{
		TaskName: "修改生日",
		TaskExpectResult: &TaskExpectResult{
			Text:  "生日修改成功",
			Image: "image_path",
		},
		TaskStepByStep: []*TaskStep{
			{
				Step: "点击我tab",
				ExpectResult: &TaskExpectResult{
					Text:  "进入个人中心",
					Image: "image_path",
				},
			},
			{
				Step: "点击编辑资料",
				ExpectResult: &TaskExpectResult{
					Text:  "进入资料编辑页面",
					Image: "image_path",
				},
			},
		},
		TaskAggregationStep: "1.点击我tab 2.点击编辑资料 3.修改生日",
		AgentType:           AgentTypeAndroid,
		AgentConfigID:       "tt",
		Device: &Device{
			Type: DeviceTypeAndroid,
			Android: &AndroidDevice{
				URL: "127.0.0.1:5555",
			},
		},
		AppID:                   "tt_voice",
		AppName:                 "TT语音",
		AppDescription:          "TT语音是一款专为游戏玩家打造的语音社交软件...",
		UIComponentInstructions: "1.***目标选择器*** -- 组件结构...",
		SpecialScenarios:        "***遇到以下情况时必须流程规避处理: ** -- 弹窗页面出现弹窗...",
		Restart:                 "",
	}
	
	assert.NotNil(t, req)
	assert.Equal(t, "修改生日", req.TaskName)
	assert.Equal(t, AgentTypeAndroid, req.AgentType)
	assert.Equal(t, DeviceTypeAndroid, req.Device.Type)
	assert.Equal(t, "127.0.0.1:5555", req.Device.Android.URL)
	assert.Len(t, req.TaskStepByStep, 2)
}

func TestAgentType_Constants(t *testing.T) {
	assert.Equal(t, AgentType("android"), AgentTypeAndroid)
	assert.Equal(t, AgentType("ios"), AgentTypeIOS)
}

func TestDeviceType_Constants(t *testing.T) {
	assert.Equal(t, DeviceType("android"), DeviceTypeAndroid)
	assert.Equal(t, DeviceType("ios"), DeviceTypeIOS)
}

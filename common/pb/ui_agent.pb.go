// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.1
// 	protoc        v5.29.2
// source: common/ui_agent.proto

package pb

import (
	reflect "reflect"
	sync "sync"

	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UIAgentComponentExpectation UIAgent组件的期望结果
type UIAgentComponentExpectation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Text          string                 `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`   // 文本
	Image         string                 `protobuf:"bytes,2,opt,name=image,proto3" json:"image,omitempty"` // 图片
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UIAgentComponentExpectation) Reset() {
	*x = UIAgentComponentExpectation{}
	mi := &file_common_ui_agent_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIAgentComponentExpectation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIAgentComponentExpectation) ProtoMessage() {}

func (x *UIAgentComponentExpectation) ProtoReflect() protoreflect.Message {
	mi := &file_common_ui_agent_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIAgentComponentExpectation.ProtoReflect.Descriptor instead.
func (*UIAgentComponentExpectation) Descriptor() ([]byte, []int) {
	return file_common_ui_agent_proto_rawDescGZIP(), []int{0}
}

func (x *UIAgentComponentExpectation) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *UIAgentComponentExpectation) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

// UIAgentComponentStep UIAgent组件的步骤
type UIAgentComponentStep struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Content       string                       `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`         // 步骤内容
	Expectation   *UIAgentComponentExpectation `protobuf:"bytes,2,opt,name=expectation,proto3" json:"expectation,omitempty"` // 期望结果
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UIAgentComponentStep) Reset() {
	*x = UIAgentComponentStep{}
	mi := &file_common_ui_agent_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIAgentComponentStep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIAgentComponentStep) ProtoMessage() {}

func (x *UIAgentComponentStep) ProtoReflect() protoreflect.Message {
	mi := &file_common_ui_agent_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIAgentComponentStep.ProtoReflect.Descriptor instead.
func (*UIAgentComponentStep) Descriptor() ([]byte, []int) {
	return file_common_ui_agent_proto_rawDescGZIP(), []int{1}
}

func (x *UIAgentComponentStep) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *UIAgentComponentStep) GetExpectation() *UIAgentComponentExpectation {
	if x != nil {
		return x.Expectation
	}
	return nil
}

// UIAgentDevice UIAgent组件的设备信息
type UIAgentDevice struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Device:
	//
	//	*UIAgentDevice_ProjectDevice_
	//	*UIAgentDevice_UserDevice_
	Device        isUIAgentDevice_Device `protobuf_oneof:"device"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UIAgentDevice) Reset() {
	*x = UIAgentDevice{}
	mi := &file_common_ui_agent_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIAgentDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIAgentDevice) ProtoMessage() {}

func (x *UIAgentDevice) ProtoReflect() protoreflect.Message {
	mi := &file_common_ui_agent_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIAgentDevice.ProtoReflect.Descriptor instead.
func (*UIAgentDevice) Descriptor() ([]byte, []int) {
	return file_common_ui_agent_proto_rawDescGZIP(), []int{2}
}

func (x *UIAgentDevice) GetDevice() isUIAgentDevice_Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *UIAgentDevice) GetProjectDevice() *UIAgentDevice_ProjectDevice {
	if x != nil {
		if x, ok := x.Device.(*UIAgentDevice_ProjectDevice_); ok {
			return x.ProjectDevice
		}
	}
	return nil
}

func (x *UIAgentDevice) GetUserDevice() *UIAgentDevice_UserDevice {
	if x != nil {
		if x, ok := x.Device.(*UIAgentDevice_UserDevice_); ok {
			return x.UserDevice
		}
	}
	return nil
}

type isUIAgentDevice_Device interface {
	isUIAgentDevice_Device()
}

type UIAgentDevice_ProjectDevice_ struct {
	ProjectDevice *UIAgentDevice_ProjectDevice `protobuf:"bytes,1,opt,name=project_device,json=projectDevice,proto3,oneof"`
}

type UIAgentDevice_UserDevice_ struct {
	UserDevice *UIAgentDevice_UserDevice `protobuf:"bytes,2,opt,name=user_device,json=userDevice,proto3,oneof"`
}

func (*UIAgentDevice_ProjectDevice_) isUIAgentDevice_Device() {}

func (*UIAgentDevice_UserDevice_) isUIAgentDevice_Device() {}

type UIAgentDevice_ProjectDevice struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Udid          string                 `protobuf:"bytes,1,opt,name=udid,proto3" json:"udid,omitempty"`   // 设备ID
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"` // 占用设备的令牌
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UIAgentDevice_ProjectDevice) Reset() {
	*x = UIAgentDevice_ProjectDevice{}
	mi := &file_common_ui_agent_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIAgentDevice_ProjectDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIAgentDevice_ProjectDevice) ProtoMessage() {}

func (x *UIAgentDevice_ProjectDevice) ProtoReflect() protoreflect.Message {
	mi := &file_common_ui_agent_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIAgentDevice_ProjectDevice.ProtoReflect.Descriptor instead.
func (*UIAgentDevice_ProjectDevice) Descriptor() ([]byte, []int) {
	return file_common_ui_agent_proto_rawDescGZIP(), []int{2, 0}
}

func (x *UIAgentDevice_ProjectDevice) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *UIAgentDevice_ProjectDevice) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type UIAgentDevice_UserDevice struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Udid          string                 `protobuf:"bytes,1,opt,name=udid,proto3" json:"udid,omitempty"`                                        // 设备ID
	RemoteAddress string                 `protobuf:"bytes,2,opt,name=remote_address,json=remoteAddress,proto3" json:"remote_address,omitempty"` // 设备远程连接地址
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UIAgentDevice_UserDevice) Reset() {
	*x = UIAgentDevice_UserDevice{}
	mi := &file_common_ui_agent_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UIAgentDevice_UserDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UIAgentDevice_UserDevice) ProtoMessage() {}

func (x *UIAgentDevice_UserDevice) ProtoReflect() protoreflect.Message {
	mi := &file_common_ui_agent_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UIAgentDevice_UserDevice.ProtoReflect.Descriptor instead.
func (*UIAgentDevice_UserDevice) Descriptor() ([]byte, []int) {
	return file_common_ui_agent_proto_rawDescGZIP(), []int{2, 1}
}

func (x *UIAgentDevice_UserDevice) GetUdid() string {
	if x != nil {
		return x.Udid
	}
	return ""
}

func (x *UIAgentDevice_UserDevice) GetRemoteAddress() string {
	if x != nil {
		return x.RemoteAddress
	}
	return ""
}

var File_common_ui_agent_proto protoreflect.FileDescriptor

var file_common_ui_agent_proto_rawDesc = []byte{
	0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x5f, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x6d, 0x0a, 0x1b, 0x55, 0x49, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x65,
	0x63, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x3a, 0x0a, 0x05, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x24, 0xfa, 0x42, 0x21, 0x72,
	0x1f, 0x32, 0x1a, 0x28, 0x3f, 0x3a, 0x5e, 0x75, 0x69, 0x5f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x3a, 0x2e, 0x2b, 0x3f, 0x29, 0xd0, 0x01, 0x01,
	0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x22, 0x8a, 0x01, 0x0a, 0x14, 0x55, 0x49, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x65, 0x70,
	0x12, 0x21, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x74, 0x12, 0x4f, 0x0a, 0x0b, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xef, 0x02, 0x0a, 0x0d, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x56, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x49, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x48, 0x00, 0x52,
	0x0d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x4d,
	0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x55, 0x49, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x48,
	0x00, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x1a, 0x4f, 0x0a,
	0x0d, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d,
	0x0a, 0x04, 0x75, 0x64, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64, 0x12, 0x1f, 0x0a,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x40, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x1a, 0x5c,
	0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x04,
	0x75, 0x64, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x72,
	0x05, 0x18, 0x40, 0xd0, 0x01, 0x01, 0x52, 0x04, 0x75, 0x64, 0x69, 0x64, 0x12, 0x2e, 0x0a, 0x0e,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0d, 0x72,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x08, 0x0a, 0x06,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x42, 0x3c, 0x5a, 0x3a, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62,
	0x2e, 0x74, 0x74, 0x79, 0x75, 0x79, 0x69, 0x6e, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x54, 0x65, 0x73,
	0x74, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x72, 0x6f,
	0x62, 0x65, 0x2d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_common_ui_agent_proto_rawDescOnce sync.Once
	file_common_ui_agent_proto_rawDescData = file_common_ui_agent_proto_rawDesc
)

func file_common_ui_agent_proto_rawDescGZIP() []byte {
	file_common_ui_agent_proto_rawDescOnce.Do(func() {
		file_common_ui_agent_proto_rawDescData = protoimpl.X.CompressGZIP(file_common_ui_agent_proto_rawDescData)
	})
	return file_common_ui_agent_proto_rawDescData
}

var file_common_ui_agent_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_common_ui_agent_proto_goTypes = []any{
	(*UIAgentComponentExpectation)(nil), // 0: common.UIAgentComponentExpectation
	(*UIAgentComponentStep)(nil),        // 1: common.UIAgentComponentStep
	(*UIAgentDevice)(nil),               // 2: common.UIAgentDevice
	(*UIAgentDevice_ProjectDevice)(nil), // 3: common.UIAgentDevice.ProjectDevice
	(*UIAgentDevice_UserDevice)(nil),    // 4: common.UIAgentDevice.UserDevice
}
var file_common_ui_agent_proto_depIdxs = []int32{
	0, // 0: common.UIAgentComponentStep.expectation:type_name -> common.UIAgentComponentExpectation
	3, // 1: common.UIAgentDevice.project_device:type_name -> common.UIAgentDevice.ProjectDevice
	4, // 2: common.UIAgentDevice.user_device:type_name -> common.UIAgentDevice.UserDevice
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_common_ui_agent_proto_init() }
func file_common_ui_agent_proto_init() {
	if File_common_ui_agent_proto != nil {
		return
	}
	file_common_ui_agent_proto_msgTypes[2].OneofWrappers = []any{
		(*UIAgentDevice_ProjectDevice_)(nil),
		(*UIAgentDevice_UserDevice_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_common_ui_agent_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_ui_agent_proto_goTypes,
		DependencyIndexes: file_common_ui_agent_proto_depIdxs,
		MessageInfos:      file_common_ui_agent_proto_msgTypes,
	}.Build()
	File_common_ui_agent_proto = out.File
	file_common_ui_agent_proto_rawDesc = nil
	file_common_ui_agent_proto_goTypes = nil
	file_common_ui_agent_proto_depIdxs = nil
}

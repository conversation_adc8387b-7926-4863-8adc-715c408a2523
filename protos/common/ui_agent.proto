syntax = "proto3";

package common;

option go_package = "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb";

import "validate/validate.proto";


// UIAgentComponentExpectation UIAgent组件的期望结果
message UIAgentComponentExpectation {
  string text = 1; // 文本
  string image = 2 [(validate.rules).string = {ignore_empty: true pattern: "(?:^ui_agent_image_id:.+?)"}]; // 图片
}

// UIAgentComponentStep UIAgent组件的步骤
message UIAgentComponentStep {
  string content = 1 [(validate.rules).string = {min_len: 1}]; // 步骤内容
  UIAgentComponentExpectation expectation = 2 [(validate.rules).message = {required: true}]; // 期望结果
}

// UIAgentDevice UIAgent组件的设备信息
message UIAgentDevice {
  message ProjectDevice {
    string udid = 1 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 设备ID
    string token = 2 [(validate.rules).string = {min_len: 1, max_len: 64}]; // 占用设备的令牌
  }
  message UserDevice {
    string udid = 1 [(validate.rules).string = {ignore_empty: true, max_len: 64}]; // 设备ID
    string remote_address = 2 [(validate.rules).string = {min_len: 1}]; // 设备远程连接地址
  }

  oneof device {
    ProjectDevice project_device = 1 [(validate.rules).message = {required: true}];
    UserDevice user_device = 2 [(validate.rules).message = {required: true}];
  }
}

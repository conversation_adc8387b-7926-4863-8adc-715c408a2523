package dispatcherlogic

import (
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
)

var _ Publisher = (*UIAgentComponentPublisher)(nil)

type UIAgentComponentPublisher struct {
	*BasePublisher
}

func (p *UIAgentComponentPublisher) CreateRecord(in *pb.PublishReq) (err error) {
	//TODO implement me
	panic("implement me")
}

func (p *UIAgentComponentPublisher) Publish(in *pb.PublishReq) (out *pb.PublishResp, err error) {
	//TODO implement me
	panic("implement me")
}

func (p *UIAgentComponentPublisher) Panic(in *pb.PublishReq, err error) {
	//TODO implement me
	panic("implement me")
}
